{"ALL_COMPILER_OPTIONS_6917": "TÜM DERLEYİCİ SEÇENEKLERİ", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Bir '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, içeri aktarma bildirimiyle birlikte kullanılamaz.", "A_0_parameter_must_be_the_first_parameter_2680": "Bir '{0}' parametresi ilk parametre olmalıdır.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "JSDoc '@template' etiketi, '@typedef', '@callback' veya '@overload' etiketini takip edemez", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "JSDoc '@typedef' açıklaması, birden çok '@type' etiketi içeremez.", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "'bigint' sabit <PERSON>, özellik adı olarak kullanılamaz.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Bir büyük tamsayı sabit değerinde üstel gösterim kullanılamaz.", "A_bigint_literal_must_be_an_integer_1353": "Büyük tamsayı sabit değeri bir tamsayı olmalıdır.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Uygulama imzasındaki bir bağlama deseni parametresi isteğe bağlı olamaz.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "'break' deyimi yalnızca bir kapsayan yineleme veya switch deyimi içinde kullanılabilir.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "'break' <PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> kapsayan deyimin etiketine atlayabi<PERSON>.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "Karakter sınıfı ayrılmış bir çift noktalayıcı içermemelidir. Ters eğik çizgiyle bundan kaçmak mı istediniz?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "Bir karakter sınıfı aralığı başka bir karakter sınıfı tarafından sınırlanmamalıdır.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "<PERSON><PERSON> sınıf, is<PERSON><PERSON><PERSON> bağlı tür bağ<PERSON>ms<PERSON>z değişkenleri ile yalnızca bir tanımlayıcıyı/tam adı uygulayabilir.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "B<PERSON> sınıf, yalnızca statik olarak bilinen üyelere sahip bir nesne türünü veya nesne türlerinin bir kesişimini uygulayabilir.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "Bir sınıf '{0}' gibi ilkel bir türü genişletemez. Sınıflar yalnızca oluşturulabilen değerleri genişletebilir.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "Bir sınıf '{0}' gibi ilkel bir türü uygulayamaz. Yalnızca diğer adlandırılmış nesne türlerini uygulayabilir.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "'default' değiştiricisi olmayan bir sınıf bildiriminin adı olmalıdır.", "A_class_member_cannot_have_the_0_keyword_1248": "Bir sınıf <PERSON> '{0}' anahtar kelimesini içeremez.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Hesaplanan özellik adında virgül ifadesine izin verilmez.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Hesaplanan özellik adı, kapsayan türündeki bir tür parametresine başvuramaz.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "<PERSON><PERSON><PERSON><PERSON>f özelliği bildirimindeki hesaplanan özellik adı, basit bir sabit değer türüne veya 'benzersiz sembol' türüne sahip olmalıdır.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Bir metot aşırı yüklemesindeki hesaplanan özellik adı, sabit değer türündeki veya 'unique symbol' türündeki bir ifadeye başvurmalıdır.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Bir tür sabit değerindeki hesaplanan özellik adı, sabit değer türündeki veya 'unique symbol' türündeki bir ifadeye başvurmalıdır.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Bir çevresel bağlamdaki hesaplanan özellik adı, sabit değer türündeki veya 'unique symbol' türündeki bir ifadeye başvurmalıdır.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Bir arabirimdeki hesaplanan özellik adı, sabit değer türündeki veya 'unique symbol' türündeki bir ifadeye başvurmalıdır.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Hesaplanan özellik adı 'string', 'number', 'symbol' veya 'any' türünde olmalıdır.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "'const' on<PERSON><PERSON><PERSON><PERSON><PERSON> yalnızca sabit listesi üyelerine veya dize, say<PERSON>, <PERSON><PERSON><PERSON>, dizi ya da nesne sabit değerlerine yönelik başvurulara uygulanabilir.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "const numa<PERSON><PERSON><PERSON><PERSON> üyesine yalnızca dize sabit değeri kullanılarak erişilebilir.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Çevresel bağlamdaki 'const' başlatıcısı bir dize veya sayısal sabit değer ya da sabit değer sabit listesi başvurusu olmalıdır.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Sınıfı 'null' değerini aşan bir oluşturucu 'super' çağrısını içeremez.", "A_constructor_cannot_have_a_this_parameter_2681": "Bir oluşturucu 'this' parametresini içeremez.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "'continue' deyimi yalnızca bir kapsayan yineleme deyimi içinde kullanılabilir.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "'continue' de<PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> kapsayan yineleme deyiminin etiketine atlayabilir.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "Bir beyan dosyası 'import type' olmadan içe aktarılamaz. Bunun yerine '{0}' uygulama dosyasını mı içe aktarmak istediniz?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Çevresel bağlamda 'declare' değiştiricisi kullanılamaz.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Dekoratörler yalnızca metot uygulaması dekore edebilir; aşırı yüklemeleri dekore edemez.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "'default' yan t<PERSON>, 'switch' deyi<PERSON>e birden fazla kez bulu<PERSON>az.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Varsayılan dışarı aktarma, yalnızca ECMAScript stili bir modülde kullanılabilir.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Varsayılan dışarı aktarma, bir dosyanın veya modül bildiriminin en üst düzeyinde olmalıdır.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "Bu bağlamda '!' beli<PERSON>i atama onayına izin verilmez.", "A_destructuring_declaration_must_have_an_initializer_1182": "Yok etme bildiriminin bir başlatıcısı olmalıdır.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "ES5'teki dinamik içe aktarma çağrısı 'Promise' oluşturucusu gerektirir.  'Promise' oluşturucusu için bir bildiriminiz olduğundan emin olun veya '--lib' seçeneğinize 'ES2015' ekleyin.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Dinamik içeri aktarma çağrısı, 'Promise' dö<PERSON><PERSON><PERSON><PERSON><PERSON>. 'Promise' için bir bildiriminiz olduğundan emin olun veya '--lib' seçeneğinize 'ES2015' ekleyin.", "A_file_cannot_have_a_reference_to_itself_1006": "<PERSON><PERSON> <PERSON>ya kendine ba<PERSON><PERSON><PERSON>.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "'never' d<PERSON><PERSON><PERSON><PERSON> bir <PERSON>, erişilebilir bir uç noktaya sahip olamaz.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "'New' an<PERSON><PERSON> k<PERSON> ç<PERSON><PERSON><PERSON><PERSON><PERSON> bir <PERSON>, 'void' olan bir 'this' tür<PERSON>ne sahip olamaz.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "<PERSON><PERSON><PERSON><PERSON><PERSON> türü 'undefined', 'void' veya 'any' o<PERSON>yan bir i<PERSON><PERSON> bir değ<PERSON> döndürmelidir.", "A_generator_cannot_have_a_void_type_annotation_2505": "Bir oluşturucu 'void' türündeki bir ek açıklamaya sahip olamaz.", "A_get_accessor_cannot_have_parameters_1054": "Bir 'get' er<PERSON>ş<PERSON><PERSON>i parametrelere sahip olamaz.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Get erişimcisi en az ayarlayıcı kadar erişilebilir olmalıdır", "A_get_accessor_must_return_a_value_2378": "'get' er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bir değer döndürmesi gere<PERSON>r.", "A_label_is_not_allowed_here_1344": "'Burada etikete izin verilmiyor.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Etiketli demet öğesi türden sonra de<PERSON>, addan sonra ve iki nokta işaretinden önce bir soru işareti ile isteğe bağlı olarak bildirilir.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Etiketlenmiş bir demet <PERSON>, tür yerine addan önce '...' ile bekleyen olarak bild<PERSON>r.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Eşlenmiş bir tür özellik veya metot bildiremez.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Sabit listesi bildirimindeki bir üye başlatıcısı, diğer sabit listelerinde tanımlanan üyeler dahil olmak üzere kendinden sonra bildirilen üyelere başvuramaz.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Mixin sınıfının 'any[]' türünde tek bir rest parametresi içeren bir oluşturucusu olmalıdır.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Soyut yapı imzası içeren bir tür değişkeninden genişleyen mixin sınıfı da 'soyut' olarak bildirilmelidir.", "A_module_cannot_have_multiple_default_exports_2528": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>en fazla var<PERSON>ılan dışarı aktarmaya sahip olamaz.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Bir ad alanı bildiri<PERSON>, birleştirildiği sınıf veya işlevden farklı bir dosyada olamaz.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Bir ad alanı bildiri<PERSON>, birleştirildiği sınıf veya işlevden önce gelemez.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Ad alanı bildirimine yalnızca bir ad alanının veya modülün en üst düzeyinde izin verilir.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "'module' anahtar sözcüğü kullanılarak 'namespace' bildiri<PERSON> bildiri<PERSON>. Lütfen bunun yerine 'namespace' anahtar sözcüğünü kullanın.", "A_non_dry_build_would_build_project_0_6357": "-dry bayrağı kullanılmayan bir derleme '{0}' projesini derler", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "-dry bayrağı kullanılmayan bir derleme şu dosyaları siler: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "DRY dışı bir der<PERSON>e, '{0}' proje<PERSON><PERSON>ı için zaman damgalarını güncelleştirir", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Parametre başlatıcısına yalnızca bir işlevde veya oluşturucu uygulamasında izin verilir.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Parametre özelliği, rest parametresi kullanılarak bildirilemez.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Parametre özelliğine yalnızca bir oluşturucu uygulamasında izin verilir.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Parametre özelliği, ba<PERSON><PERSON>a deseni kullanılarak bildirilemez.", "A_promise_must_have_a_then_method_1059": "Promise'in bir 'then' metodu o<PERSON>.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Bir 'unique symbol' türündeki sınıfın özelliği hem 'static' hem de 'readonly' olmalıdır.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Bir 'unique symbol' türündeki arabirimin veya tür sabit değerinin özelliği 'readonly' o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "A_required_element_cannot_follow_an_optional_element_1257": "<PERSON><PERSON><PERSON><PERSON>, iste<PERSON>e bağlı öğeden sonra gelemez.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Gerekli parametre, isteğe bağlı parametreden sonra gelemez.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "rest öğesi bir bağlama deseni içeremez.", "A_rest_element_cannot_follow_another_rest_element_1265": "REST öğesi başka bir REST öğesini izleyemez.", "A_rest_element_cannot_have_a_property_name_2566": "Rest öğesinin özellik adı olamaz.", "A_rest_element_cannot_have_an_initializer_1186": "rest öğesi bir başlatıcıya sahip olamaz.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "<PERSON> öğesi, yok etme desenindeki son ö<PERSON><PERSON> olmalıdır.", "A_rest_element_type_must_be_an_array_type_2574": "REST öğesi dizi türünde olmalıdır.", "A_rest_parameter_cannot_be_optional_1047": "rest parametresi isteğe bağlı olamaz.", "A_rest_parameter_cannot_have_an_initializer_1048": "rest parametresi bir başlatıcıya sahip olamaz.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "rest parametresi, parametre listesinin sonunda bulunmalıdır.", "A_rest_parameter_must_be_of_an_array_type_2370": "rest parametresi dizi türünde olmalıdır.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Bir rest parametresi veya bağlama deseninin sonunda virgül o<PERSON>az.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "'return' <PERSON><PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> bir işlev gövdesinde kullanılabilir.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "'Return' deyimi bir sınıf statik bloğu içinde kullanılamaz.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "<PERSON><PERSON><PERSON> a<PERSON>armaları, 'baseUrl' ile ilgili arama konumlarına yeniden eşleyen bir girdi dizisi.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "'set' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON> türü ek açıklamasına sahip olamaz.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "'set' er<PERSON><PERSON><PERSON><PERSON><PERSON> isteğe bağlı bir parametreye sahip olamaz.", "A_set_accessor_cannot_have_rest_parameter_1053": "'set' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rest parametresine sahip olamaz.", "A_set_accessor_must_have_exactly_one_parameter_1049": "'set' er<PERSON><PERSON><PERSON><PERSON><PERSON> tam olarak bir parametreye sahip olmalıdır.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "'set' erişimci parametresinin bir başlatıcısı olamaz.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON>, bir demet türüne sahip olmalı ya da bir rest parametresine geçirilmelidir.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "'super' ç<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ba<PERSON><PERSON><PERSON><PERSON><PERSON>ş özellikleri, parametre özelliklerini veya özel tanımlayıcıları içeren türetilmiş bir sınıfın oluşturucusu içinde kök düzeyinde bir deyim olmalıdır.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Bir türetilmiş sınıf ba<PERSON><PERSON><PERSON><PERSON><PERSON>ş özellikler, parametre özellikleri veya özel tanımlayıcılar içerdiğinde 'super' çağrısı, 'super' veya 'this' öğesine başvurmak için oluşturucudaki ilk deyim olmalıdır.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "'This' tabanlı tür koruması, parametre tabanlı tür koruması ile uyumlu değildir.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "'this' tü<PERSON><PERSON>, yalnı<PERSON>ca bir sınıfın veya arabirimin statik olmayan bir üyesinde kullanılabilir.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "'verbatimModuleSyntax' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommonJS modülündeki değer bildirimlerinde üst düzey bir 'export' değiştiricisi kullanılamaz.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "'tsconfig.json' dosyası şu konumda zaten tanımlanmış: '{0}'.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Demet üyesi hem isteğe bağlı hem de diğerleri olamaz.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Bir demet türünün negatif bir değerle dizini oluşturulamaz.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Üs ifadesinin sol tarafında tür onaylama ifadesine izin verilmez. İfadeyi parantez içine yazmayı düşünün.", "A_type_literal_property_cannot_have_an_initializer_1247": "Tür sabit değeri özelliği bir başlatıcıya sahip olamaz.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Yalnızca tür içeri aktarma işlemleri varsayılan bir içeri aktarmayı veya adlandırılan bağlamaları belirtebilir ancak ikisini birden belirtemez.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "<PERSON><PERSON> tür k<PERSON>, rest parametresine ba<PERSON><PERSON><PERSON> yapa<PERSON>.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "<PERSON><PERSON> tür k<PERSON>, bağlama desenindeki '{0}' <PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON> ya<PERSON>.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Bir tür koşuluna yalnızca işlevlere ve metotlara ait dönüş türü konumunda izin verilir.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Bir tür koşulunun türü, parametresinin türüne atanabilir olmalıdır.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "'isolatedModules' ve 'emitDecoratorMetadata' etkinleştirildiğinde, dekore edilmiş bir imzada başvurulan bir tür 'import type' veya bir namespace import ile içeri aktarılmalıdır.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Bir 'unique symbol' türündeki değişken 'const' o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Bir 'yield' ifadesine yalnızca oluşturucu gövdesinde izin verilir.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "'{1}' sınıfındaki '{0}' soyut metoduna super ifadesi ile erişilemez.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Soyut metotlar yalnızca bir soyut sınıfta görüntülenebilir.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "Soyut metotlar yalnızca bir soyut sınıfta görüntülenebilir.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "'{1}' sınıfındaki '{0}' soyut özelliğine oluşturucuda erişilemiyor.", "Accessibility_modifier_already_seen_1028": "Erişilebilirlik değiştiricisi zaten görüldü.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Erişimciler yalnızca ECMAScript 5 ve üzeri hedeflenirken kullanılabilir.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "İki erişimci de soyut veya soyut olmayan olmalıdır.", "Add_0_to_unresolved_variable_90008": "Çözümlenmemiş değişkene '{0}.' ekle", "Add_a_return_statement_95111": "Return deyimi e<PERSON>", "Add_a_return_type_to_the_function_declaration_9031": "İşlev bildirimine bir dönüş türü e<PERSON>.", "Add_a_return_type_to_the_function_expression_9030": "<PERSON><PERSON><PERSON> if<PERSON> bir dön<PERSON>ş tür<PERSON> e<PERSON>.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "Get erişimcisi bildirimine bir dön<PERSON>ş türü e<PERSON>.", "Add_a_return_type_to_the_method_9034": "Yönteme bir dön<PERSON><PERSON> türü e<PERSON>", "Add_a_type_annotation_to_the_parameter_0_9028": "{0} parametresine bir tür ek açıklaması ekleyin.", "Add_a_type_annotation_to_the_property_0_9029": "{0} özelliğine bir tür ek açıklaması ekleyin.", "Add_a_type_annotation_to_the_variable_0_9027": "{0} değişkenine bir tür ek açıklaması ekleyin.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "Ayarlanmış erişimci bildiriminin parametresine bir tür e<PERSON>.", "Add_all_missing_async_modifiers_95041": "<PERSON><PERSON><PERSON> e<PERSON><PERSON> '<PERSON><PERSON>' <PERSON>ğiştiric<PERSON>i ekle", "Add_all_missing_attributes_95168": "Tüm eksik öznitelikleri ekleyin", "Add_all_missing_call_parentheses_95068": "Eksik tüm çağrı parantezlerini ekle", "Add_all_missing_function_declarations_95157": "Eksik işlev bildirimlerinin tümünü ekle", "Add_all_missing_imports_95064": "Tüm eksik içeri aktarmaları ekleyin", "Add_all_missing_members_95022": "Tüm eksik üyeleri ekle", "Add_all_missing_override_modifiers_95162": "<PERSON><PERSON><PERSON> e<PERSON><PERSON> 'override' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ekle", "Add_all_missing_parameters_95190": "Tüm eksik parametreleri ekleyin", "Add_all_missing_properties_95166": "Tüm eksik özellikleri ekleyin", "Add_all_missing_return_statement_95114": "<PERSON><PERSON>m eksik return de<PERSON><PERSON><PERSON>", "Add_all_missing_super_calls_95039": "Tüm eksik süper çağrıları ekle", "Add_all_missing_type_annotations_90067": "Eksik olan tüm tür ek açıklamalarını ekleyin", "Add_all_optional_parameters_95193": "<PERSON><PERSON>m isteğe bağlı parametreleri ekleyin", "Add_annotation_of_type_0_90062": "'{0}' türünde ek açıklama ekleyin", "Add_async_modifier_to_containing_function_90029": "İçeren işleve zaman uyumsuz değiştirici ekle", "Add_await_95083": "'await' e<PERSON>in", "Add_await_to_initializer_for_0_95084": "'{0}' i<PERSON><PERSON> b<PERSON> 'await' <PERSON><PERSON><PERSON>", "Add_await_to_initializers_95089": "Başlatıcılar<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Add_braces_to_arrow_function_95059": "Ok işlevine küme ayracı ekleyin", "Add_const_to_all_unresolved_variables_95082": "Çözümlenmemiş tüm değişkenlere 'const' e<PERSON>in", "Add_const_to_unresolved_variable_95081": "Çözümlenmemiş değişkene 'const' e<PERSON><PERSON>", "Add_definite_assignment_assertion_to_property_0_95020": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> belirli atama onayı ekle", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "<PERSON>ü<PERSON> başlatılmamış özelliklere kesin atama onayları ekle", "Add_export_to_make_this_file_into_a_module_95097": "Bu dosyayı bir modüle dönüştürmek için 'export {}' ekleyin", "Add_extends_constraint_2211": "`extends` kısıtlaması ekleyin.", "Add_extends_constraint_to_all_type_parameters_2212": "Tüm tür parametrelerine `extends` kısıtlaması ekleyin", "Add_import_from_0_90057": "\"{0}\" kaynağından içeri aktarma ekle", "Add_index_signature_for_property_0_90017": "'{0}' özelliği için dizin imzası ekle", "Add_initializer_to_property_0_95019": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> başlatıcı ekle", "Add_initializers_to_all_uninitialized_properties_95027": "Tüm başlatılmamış özelliklere başlatıcılar ekle", "Add_missing_attributes_95167": "Eksik öznitelikleri ekleyin", "Add_missing_call_parentheses_95067": "Eksik çağrı parantezlerini ekle", "Add_missing_comma_for_object_member_completion_0_95187": "'{0}' nesne üyesinin tamamlanması için eksik virgülü ekleyin.", "Add_missing_enum_member_0_95063": "<PERSON><PERSON><PERSON> '{0}' sabit listesi <PERSON><PERSON><PERSON> e<PERSON>", "Add_missing_function_declaration_0_95156": "<PERSON><PERSON><PERSON> '{0}' <PERSON><PERSON><PERSON> bi<PERSON> ekle", "Add_missing_new_operator_to_all_calls_95072": "<PERSON><PERSON>m çağrılara eksik 'new' <PERSON><PERSON><PERSON><PERSON> ekleyin", "Add_missing_new_operator_to_call_95071": "Çağrıya eksik 'new' i<PERSON><PERSON><PERSON> ekleyin", "Add_missing_parameter_to_0_95188": "'{0}' öğesine eksik parametreyi ekleyin", "Add_missing_parameters_to_0_95189": "'{0}' öğesine eksik parametreleri ekleyin", "Add_missing_properties_95165": "Eksik özellikleri ekleyin", "Add_missing_super_call_90001": "Eksik 'super()' çağrısını ekle", "Add_missing_typeof_95052": "<PERSON><PERSON><PERSON> '<PERSON><PERSON>' <PERSON><PERSON><PERSON>", "Add_names_to_all_parameters_without_names_95073": "Adları olmayan tüm parametrelere ad ekleyin", "Add_optional_parameter_to_0_95191": "'{0}' ö<PERSON><PERSON><PERSON> isteğe bağlı parametre ekleme", "Add_optional_parameters_to_0_95192": "'{0}' ö<PERSON><PERSON><PERSON> isteğe bağlı parametreler ekle", "Add_or_remove_braces_in_an_arrow_function_95058": "Ok işlevine küme ayracı ekle veya kaldır", "Add_override_modifier_95160": "'override' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Add_parameter_name_90034": "Parametre adı ekleyin", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Bir üye adıyla eşleşen tüm çözülmemiş değişkenlere niteleyici ekle", "Add_resolution_mode_import_attribute_95196": "'resolution-mode' içeri aktarma özniteliği ekle", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "'resolution-mode' içeri aktarma özniteliğini, bunun gerekliği olduğu tüm yalnızca tür içeri aktarma işlemlerine ekle", "Add_return_type_0_90063": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> tipi e<PERSON>in", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "<PERSON><PERSON><PERSON><PERSON> açık hale getirmek için bu ifadeye satisfies operatörü ve bir tür iddiası ekleyin (satisfies T as T).", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "Satisfies operatörü ve '{0}' içeren satır içi tip iddiası ekleyin", "Add_to_all_uncalled_decorators_95044": "Çağ<PERSON><PERSON><PERSON>yan tüm dekoratörlere '()' ekle", "Add_ts_ignore_to_all_error_messages_95042": "Tüm hata iletilerine '@ts-ignore' ekle", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "<PERSON><PERSON><PERSON> k<PERSON>ak erişildiğinde türe 'undefined' ekle.", "Add_undefined_to_optional_property_type_95169": "İsteğe bağlı özellik türüne 'undefined' ekleyin", "Add_undefined_type_to_all_uninitialized_properties_95029": "Tü<PERSON> başlatılmamış özelliklere tanımsız tür ekle", "Add_undefined_type_to_property_0_95018": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> 'undefined' tü<PERSON><PERSON><PERSON><PERSON> ekle", "Add_unknown_conversion_for_non_overlapping_types_95069": "Çak<PERSON>ş<PERSON><PERSON> türler için 'unknown' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Çak<PERSON>şmayan türlerin tüm dö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 'unknown' e<PERSON><PERSON>", "Add_void_to_Promise_resolved_without_a_value_95143": "<PERSON><PERSON><PERSON>n Promise'e 'void' ekle", "Add_void_to_all_Promises_resolved_without_a_value_95144": "<PERSON><PERSON><PERSON>n tüm Promise'lere 'void' ekle", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Bir tsconfig.json dosyası eklemek, hem TypeScript hem de JavaScript dosyaları içeren projeleri düzenlemenize yardımcı olur. Daha fazla bilgi edinmek için bkz. https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Tüm '{0}' bi<PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON> kısıtl<PERSON>lara sahip olmalıdır.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Tüm '{0}' bild<PERSON>mleri aynı değiştiricilere sahip olmalıdır.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Tüm '{0}' bildirimleri özdeş tür parametrelerine sahip olmalıdır.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Soyut metoda ait tüm bildirimler ardışık olmalıdır.", "All_destructured_elements_are_unused_6198": "Yapısı bozulan öğelerin hiçbiri kullanılmıyor.", "All_imports_in_import_declaration_are_unused_6192": "İçeri aktarma bildirimindeki hiçbir içeri aktarma kullanılmadı.", "All_type_parameters_are_unused_6205": "<PERSON><PERSON><PERSON> tür parametreleri kullanılmıyor.", "All_variables_are_unused_6199": "Hiçbir değişken kullanılmıyor.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "JavaScript dosyalarının programınızın bir parçası olmasına izin verin. Bu dosyalardan hata almak için 'checkJS' seçeneğ<PERSON> kullanın.", "Allow_accessing_UMD_globals_from_modules_6602": "Modüllerden UMD genel değişkenlerine erişmeye izin verin.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Varsayılan dışarı aktarmaya sahip olmayan modüllerde varsayılan içeri aktarmalara izin verin. Bu işlem kod üretimini etkilemez, yalnızca tür denetimini etkiler.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Bir modülün varsayılan dışarı aktarması olmadığında 'import x from y' öğesine izin verin.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "tsli<PERSON><PERSON>den yardımcı işlevlerin her dosya başına eklenmesi yerine proje başına bir kez içeri aktarılmasına izin verin.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "İçe aktarma işlemlerinin TypeScript dosya uzantılarını içermesine izin verin. '--moduleResolution bundler' ve '--noEmit' veya '--emitDeclarationOnly' ayarını gerektirir.", "Allow_javascript_files_to_be_compiled_6102": "Javascript dosyalar<PERSON><PERSON><PERSON>n derlenmesine izin ver.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Modüller çözümlenirken birden çok klasörün tek bir klasör olarak işlenmesine izin verin.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Zaten eklenmiş olan '{0}' dosya adı, '{1}' dosya adından yalnızca büyük/küçük harf yönünden farklıdır.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Çevresel modül bildirimi göreli modül adını belirtemez.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>, <PERSON><PERSON><PERSON> modüllerde veya ad alanlarında iç içe bulunamaz.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "AMD modülü birden fazla ad atamasında sahip olamaz.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Soyut erişimcinin uygulaması olamaz.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Erişilebilirlik değiştiricisi özel bir tanımlayıcıyla kullanılamıyor.", "An_accessor_cannot_have_type_parameters_1094": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tür parametrelerine sa<PERSON> olamaz.", "An_accessor_property_cannot_be_declared_optional_1276": "'accessor' <PERSON><PERSON><PERSON><PERSON><PERSON> isteğe bağlı olarak bildirilemez.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Çevresel modül bildirimine yalnızca bir dosyadaki en üst düzeyde izin verilir.", "An_argument_for_0_was_not_provided_6210": "'{0}' i<PERSON><PERSON> b<PERSON><PERSON>z değişken sağlanmadı.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Bu bağlama deseniyle eşleşen bağımsız değişken sağlanmadı.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Aritmetik işlenen 'any', 'number', 'bigint' veya bir sabit listesi türünde olmalıdır.", "An_arrow_function_cannot_have_a_this_parameter_2730": "<PERSON> işlevi 'this' parametresine sahip olamaz.", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "ES5'teki e<PERSON> bir i<PERSON><PERSON> veya yönte<PERSON>, 'Promise' yapı<PERSON>ısını gerektirir.  'Promise' oluşturucusu için bir bildiriminiz olduğundan emin olun veya '--lib' seçeneğinize 'ES2015' ekleyin.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Zaman uyumsuz bir işlevin veya metodun 'Promise' d<PERSON><PERSON><PERSON><PERSON><PERSON> gere<PERSON>. 'Promise' için bir bildiriminiz olduğundan emin olun veya '--lib' seçeneğinize 'ES2015' ekleyin.", "An_async_iterator_must_have_a_next_method_2519": "Zaman uyumsuz yineleyicinin bir 'next()' metodu o<PERSON>.", "An_element_access_expression_should_take_an_argument_1011": "Bir öğe erişimi ifadesi bir bağımsız değişken almalıdır.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Sabit listesi üyesi özel bir tanımlayıcıyla adlandırılamaz.", "An_enum_member_cannot_have_a_numeric_name_2452": "Sabit listesi ü<PERSON>i, sayısal bir ada sa<PERSON> olamaz.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Sabit listesi üyesinin adından sonra bir ',', '=' veya '}' gelmelidir.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Bu bilgilerin genişletilmiş bir versiyonu, kullanılabilir tüm derleyici seçeneklerini g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Dışarı aktarma ataması, dışarı aktarılmış diğer öğelere sahip bir modülde kullanılamaz.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Ad alanında dışarı aktarma ataması kullanılamaz.", "An_export_assignment_cannot_have_modifiers_1120": "Dışarı aktarma ataması, değiştiricilere sahip olamaz.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Dışarı aktarma ataması, bir dosyanın veya modül bildiriminin en üst düzeyinde olmalıdır.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Dışarı aktarma bildirimi yalnızca modülün en üst düzeyinde kullanılabilir.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Dışarı aktarma bildirimi yalnızca bir ad alanının veya modülün en üst düzeyinde kullanılabilir.", "An_export_declaration_cannot_have_modifiers_1193": "Dışarı aktarma bild<PERSON>mi, değiştiricilere sahip olamaz.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "'VerbatimModuleSyntax' etkinleştirildiğinde 'export =' bildirimi gerçek bir değere başvurmalıdır ancak '{0}' yalnızca tür bildirimine çözümleniyor.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "'VerbatimModuleSyntax' etkinleştirildiğinde 'export =' bildirimi bir değere başvuruda bulunmalıdır ancak '{0}' yalnızca bir türe başvuruyor.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "'VerbatimModuleSyntax' etkinleştirildiğinde 'export default' gerçek bir değere başvurmalıdır ancak '{0}' yalnızca tür bildirimine çözümleniyor.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "'VerbatimModuleSyntax' etkinleştirildiğinde 'dışa aktarma varsayılanı' bir de<PERSON>ere başvuruda bulunmalıdır ancak '{0}' yalnızca bir türe başvuruyor.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "'void' tü<PERSON><PERSON><PERSON> bir ifade doğruluk bakımından test edilemiyor.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Genişletilmiş Unicode kaçış değeri, 0x0 ve 0x10FFFF dahil olmak üzere bu değerler arasında olmalıdır.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Tanımlayıcı veya anahtar sözcük, sayı<PERSON> bir sabit değerden hemen sonra gelemez.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "<PERSON><PERSON><PERSON><PERSON><PERSON>, çev<PERSON>el bağlamda bildirilemez.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "İçeri aktarma diğer adı, 'export type' kullanılarak dışarı aktarılan bir bildirime başvuramaz.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "İçeri aktarma diğer adı, 'import type' kullanılarak içeri aktarılan bir bildirime başvuramaz.", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "Bir içe aktarma takma adı, 'verbatimModuleSyntax' etkinleştirildiğinde bir türe veya yalnızca tür bildirimine çözümlenemez.", "An_import_alias_cannot_use_import_type_1392": "İçeri aktarma diğer adı, 'import type' kullanamaz", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "İçeri aktarma bildirimi yalnızca modülün en üst düzeyinde kullanılabilir.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "İçeri aktarma bildirimi yalnızca bir ad alanının veya modülün en üst düzeyinde kullanılabilir.", "An_import_declaration_cannot_have_modifiers_1191": "İçeri aktarma <PERSON>, değiştiricilere sahip olamaz.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "Bir içe aktarma yolu yalnızca 'allowImportingTsExtensions' etkinleştirildiğinde '{0}' uzantısıyla bitebilir.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Dizin imzası bir rest parametresine sahip olamaz.", "An_index_signature_cannot_have_a_trailing_comma_1025": "<PERSON><PERSON> imzasının sonunda virgül olamaz.", "An_index_signature_must_have_a_type_annotation_1021": "Dizin imzası bir tür açıklamasına sahip olmalıdır.", "An_index_signature_must_have_exactly_one_parameter_1096": "Dizin imzası tam olarak bir parametreye sahip olmalıdır.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Dizin imzası parametresi, bir soru işareti içeremez.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Dizin imzası parametresi, bir erişilebilirlik değiştiricisine sahip olamaz.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Dizin imzası parametresi, bir başlatıcıya sahip olamaz.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Dizin imzası parametresi, bir tür ek açıklamasına sahip olmalıdır.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Dizin imzası parametre türü sabit değer veya genel tür olamaz. Bunun yerine eşlenen nesne türü kullanabilirsiniz.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "<PERSON>zin imzası parametre türü 'dize', 'sayı', 'sembol' veya şablon sabit değeri olmalıdır.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Bir örnek oluşturma ifadesinin ardından özellik erişimi gelemez.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "B<PERSON> a<PERSON>iri<PERSON>, is<PERSON><PERSON><PERSON> bağlı tür bağ<PERSON>ms<PERSON>z değişkenleri ile yalnızca bir tanımlayıcıyı/tam adı genişletebilir.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Arabirim, yalnızca statik olarak bilinen üyelere sahip bir nesne türünü veya nesne türlerinin bir kesişimini genişletebilir.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "Bir arayüz '{0}' gibi ilkel bir türü genişletemez. Yalnızca diğer adlandırılmış nesne türlerini genişletebilir.", "An_interface_property_cannot_have_an_initializer_1246": "Arabirim özelliği bir başlatıcıya sahip olamaz.", "An_iterator_must_have_a_next_method_2489": "B<PERSON> yineleyici 'next()' metoduna sahip olmalıdır.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "JSX parçalarıyla @jsx pragması kullanılırken bir @jsxFrag pragması gerekir.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Nesne sabit değeri aynı ada sahip birden fazla get/set eriş<PERSON><PERSON>ine sahip olamaz.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Nesne sabit değerinin aynı ada sahip birden fazla özelliği olamaz.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Nesne sabit değ<PERSON>, aynı ada sahip bir özellik ve erişimciye sahip olamaz.", "An_object_member_cannot_be_declared_optional_1162": "<PERSON><PERSON><PERSON> ü<PERSON>i, is<PERSON><PERSON><PERSON> bağlı olarak bildirilemez.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "Bir nesnenin '[Symbol.hasInstance]' y<PERSON><PERSON><PERSON><PERSON>, bir 'instanceof' ifadesinin sağ tarafında kullanılabilmesi için bir boole değeri döndürmesi gereklidir.", "An_optional_chain_cannot_contain_private_identifiers_18030": "İsteğe bağlı bir zincir, özel tanımlayıcı içeremez.", "An_optional_element_cannot_follow_a_rest_element_1266": "İsteğe bağlı bir öğe, REST öğesini izleyemez.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "'this' ö<PERSON><PERSON>nin dış değeri bu kapsayıcı tarafından gölgelenir.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Aşırı yü<PERSON>me imzası, bir oluşturucu olarak bildirilemez.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Üs ifadesinin sol tarafında '{0}' işlecine sahip bir tek terimli ifadeye izin verilmez. İfadeyi parantez içine yazmayı düşünün.", "Annotate_everything_with_types_from_JSDoc_95043": "Her şeye JSDoc'tan türler ile not ekle", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "Ad alanındaki expando fonksiyonunun özellik türlerine not ekleyin", "Annotate_with_type_from_JSDoc_95009": "<PERSON><PERSON><PERSON> türü ile not ekle", "Another_export_default_is_here_2753": "Başka bir dışarı aktarma varsayılanını burada bulabilirsiniz.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "Tek bir karakterden daha fazlasıyla eşleşebilecek herhangi bir Unicode özelliği, yalnızca Unicode Kümeleri (v) bayrağı ayarlandığında kullanılabilir.", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "Tek bir karakterden fazlasıyla eşleşmesi muhtemel olan herhangi bir <PERSON>ey, negatif karakter sınıfı içinde geçersizdir.", "Are_you_missing_a_semicolon_2734": "Noktalı virgülünüz eksik mi?", "Argument_expression_expected_1135": "Bağımsız değişken ifadesi bekleniyor.", "Argument_for_0_option_must_be_Colon_1_6046": "'{0}' se<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>ı<PERSON><PERSON>z değişkeni {1} olmalıdır.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "Dinamik içeri aktarmanın bağımsız değişkeni yayılma öğesi olamaz.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "'{0}' türündeki bağımsız değişken '{1}' türündeki parametreye atanamaz.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "'{0}' tür<PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> değişkeni 'exactOptionalPropertyTypes: true' ile '{1}' türündeki parametreye atanamaz. Hedef özelliklerinin türlerine 'undefined' ekle<PERSON><PERSON> deneyin.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "'{0}' rest parametresinin bağımsız değişkenleri sağlanmadı.", "Array_element_destructuring_pattern_expected_1181": "<PERSON><PERSON> ö<PERSON> yok etme deseni bekleniyor.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "<PERSON><PERSON><PERSON><PERSON> sahip diziler --isolatedDeclarations ile çıkarsanamaz.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ç<PERSON><PERSON><PERSON><PERSON> hede<PERSON><PERSON>i her adın açık bir tür ek açıklaması ile bildirilmesini gerektirir.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "<PERSON><PERSON><PERSON><PERSON><PERSON>, çağrı hedefinin bir tanımlayıcı veya tam ad olmasını gerektirir.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "Özelliklerin işlevlere bildirilmeden atanması --isolatedDeclarations ile desteklenmez. Bu işleve atanan özellikler için açık bir bildirim ekleyin.", "Asterisk_Slash_expected_1010": "'*/' bekle<PERSON>yor.", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "En az bir erişimcinin --isolatedDeclarations ile açık bir tür ek açıklamasına sahip olması gereklidir.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "<PERSON>l kapsam genişletmeleri yalnızca dış modüllerde ya da çevresel modül bildirimlerinde doğrudan yuvalanabilir.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "<PERSON><PERSON> g<PERSON>, zaten çev<PERSON>el olan bir bağlamda göründükleri durumlar dışında 'declare' değiştiricisine sahip olmalıdır.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "'{0}' projesinde otomatik tür bulma etkinleştirildi. '{2}' önbellek konumu kullanılarak '{1}' modülü için ek çözümleme geçişi çalıştırılıyor.", "BUILD_OPTIONS_6919": "DERLEME SEÇENEKLERİ", "Backwards_Compatibility_6253": "Geriye Doğru Uyumluluk", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Temel sınıf ifade<PERSON>inde sınıf türü parametrelerine başvuruda bulunulamaz.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "'{0}' temel oluş<PERSON>ucu dö<PERSON> türü, statik olarak bilinen üyelere sahip bir nesne türü veya nesne türlerinin bir kesişimi <PERSON>.", "Base_constructors_must_all_have_the_same_return_type_2510": "Tüm temel oluşturucuların aynı dönüş türüne sahip olması gerekir.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Mutlak olmayan modül adlarını çözümlemek için kullanılan temel dizin.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "ES2020'den düşük değerler hedeflendiğinde büyük tamsayı sabit değerleri kullanılamıyor.", "Binary_digit_expected_1177": "İkili sayı bekleniyor.", "Binding_element_0_implicitly_has_an_1_type_7031": "'{0}' b<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> olarak '{1}' tür<PERSON> içeriyor.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "Bağlama öğeleri do<PERSON> --isolatedDeclarations ile dışa aktarılamaz.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Blok kapsamlı değişken '{0}', bild<PERSON>lmeden önce kullanıldı.", "Build_a_composite_project_in_the_working_directory_6925": "Çalışma dizininde kompozit proje oluşturun.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "<PERSON><PERSON><PERSON><PERSON> gö<PERSON>ü<PERSON>ler de dahil olmak üzere tüm projeleri derleyin.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "<PERSON><PERSON><PERSON><PERSON>, bir veya daha fazla projeyi ve bağımlılıklarını derleyin", "Build_option_0_requires_a_value_of_type_1_5073": "'{0}' <PERSON><PERSON><PERSON>, {1} tür<PERSON>nde bir değer gerektiriyor.", "Building_project_0_6358": "'{0}' projesi derleniyor...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>, 'any' yerine 'undefined' 'TReturn' türü ile örnekleniyor.", "COMMAND_LINE_FLAGS_6921": "KOMUT SATIRI BAYRAKLARI", "COMMON_COMMANDS_6916": "ORTAK KOMUTLAR", "COMMON_COMPILER_OPTIONS_6920": "ORTAK DERLEYİCİ SEÇENEKLERI", "Call_decorator_expression_90028": "Dekoratör ifadesini ç<PERSON>ğır", "Call_signature_return_types_0_and_1_are_incompatible_2202": "'{0}' ve '{1}' ça<PERSON><PERSON><PERSON> imzası dönüş türleri uyumsuz.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Dönüş türü ek açıklaması bulunmayan çağrı imzası, örtük olarak 'any' dönüş türüne sahip.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Bağımsız değişken içermeyen çağrı imzaları uyumsuz '{0}' ve '{1}' dönüş türlerine sahip.", "Can_only_convert_logical_AND_access_chains_95142": "Yalnızca mantıksal zincirler VE erişim zincirleri dönüştürülebilir", "Can_only_convert_named_export_95164": "Yalnızca adı belirtilen dışarı aktarma dönüştürülebilir", "Can_only_convert_property_with_modifier_95137": "Yalnızca değiştirici içeren özellik dönüştürülebilir", "Can_only_convert_string_concatenations_and_string_literals_95154": "Yalnızca dize birleştirmelerini ve dize değişmezlerini dönüştürebilir", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "'{0}' bir ad alanı değil tür olduğundan '{0}.{1}' eriş<PERSON>i sa<PERSON>mıyor. '{0}[\"{1}\"]' değerini belirterek '{0}' içindeki '{1}' özelliğinin türünü almak mı istediniz?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "'{1}' etkinleştirildiğinde başka bir dosyadan '{0}' öğesine niteleme olmadan erişilemez. Bunun yerine '{2}' k<PERSON><PERSON><PERSON>n.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "'{0}' etkinleştirildiğinde ortam sabit listelerine erişilemiyor.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "'{0}' olu<PERSON><PERSON><PERSON><PERSON> türü<PERSON> '{1}' olu<PERSON><PERSON><PERSON>u türü at<PERSON>.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Bir soyut oluşturucu türü, soyut olmayan bir oluşturucu türüne atanamaz.", "Cannot_assign_to_0_because_it_is_a_class_2629": "<PERSON><PERSON><PERSON><PERSON><PERSON> '{0}' özelliğine atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Sabit olduğundan '{0}' özelliğine atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_a_function_2630": "<PERSON><PERSON><PERSON> '{0}' özelliğine atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Ad alanı olduğundan '{0}' özelliğine atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Salt okunur bir özellik olduğundan '{0}' özelliğine atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Sabit listesi olduğundan '{0}' öğ<PERSON>ne atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_an_import_2632": "İçeri aktarma olduğundan '{0}' öğ<PERSON>ne atama yapılamıyor.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Değişken olmadığından '{0}' ö<PERSON><PERSON>ne atama yapılamıyor.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "'{0}' <PERSON>zel metoduna atanamıyor. <PERSON>zel metotlar yazılamaz.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "'{0}' m<PERSON><PERSON><PERSON><PERSON>, modül o<PERSON> bir varlığa çözümlendiğinden genişletilemiyor.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Modül olmayan bir varlığa çözümlendiğinden '{0}' m<PERSON><PERSON><PERSON><PERSON>, de<PERSON><PERSON> dışarı aktarmalarıyla genişletilemiyor.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "'--module' bayrağı 'amd' veya 'system' olmadığı sürece '{0}' seçeneği kullanılarak modül derlenemez.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Bir soyut sınıfın örneği oluşturulamaz.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>in 'next' metodu '{1}' t<PERSON><PERSON><PERSON><PERSON><PERSON> bekle<PERSON>ine ra<PERSON><PERSON> kapsayan olu<PERSON>u her zaman '{0}' gönderdiğ<PERSON><PERSON> y<PERSON><PERSON><PERSON> te<PERSON>, de<PERSON><PERSON> atanamıyor.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "'{0}' dışarı aktarılamıyor. Bir modülden yalnızca yerel bildirimler dışarı aktarılabilir.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "'{0}' sınıfı genişletilemez. Sın<PERSON>f <PERSON>, özel olarak işaretlenmiş.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "'{0}' a<PERSON><PERSON>mi genişletilemiyor. <PERSON><PERSON><PERSON> yerine 'implements' kullanmayı deneyin.", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Geçerli dizinde tsconfig.json dosyası bulunamıyor: {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Belirtilen dizinde tsconfig.json dosyası bulunamıyor: '{0}'.", "Cannot_find_global_type_0_2318": "'{0}' genel tü<PERSON><PERSON> b<PERSON>.", "Cannot_find_global_value_0_2468": "'{0}' genel <PERSON><PERSON><PERSON> b<PERSON>.", "Cannot_find_lib_definition_for_0_2726": "'{0}' i<PERSON><PERSON> kitaplık tanımı bulunamıyor.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "'{0}' i<PERSON><PERSON> kitaplık tanımı bulunamıyor. Şunu mu demek istediniz: '{1}'?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "'{0}' m<PERSON><PERSON><PERSON><PERSON> bulunamıyor. Modülü '.json' uzantısıyla içeri aktarmak için '--resolveJsonModule' kullanmayı deneyin.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "'{0}' m<PERSON><PERSON><PERSON><PERSON> bulunamıyor. 'moduleResolution' seçeneğini 'nodenext' olarak ayarlamak veya 'paths' seç<PERSON>ğine diğer adlar eklemek mi istediniz?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "'{0}' m<PERSON><PERSON><PERSON><PERSON> veya karşılık gelen tür bildirimleri bulunamıyor.", "Cannot_find_name_0_2304": "'{0}' adı bulunamıyor.", "Cannot_find_name_0_Did_you_mean_1_2552": "'{0}' adı bulunamıyor. Bunu mu demek istediniz: '{1}'?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "'{0}' adı bulunamıyor. 'this.{0}' örnek üyesini mi aradınız?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "'{0}' adı bulunamıyor. '{1}.{0}' statik üyesini mi aradınız?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "'{0}' adı bulunamadı. <PERSON>unu zaman uyumsuz bir işleve mi yazmak istediniz?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "'{0}' ad<PERSON> bulunamıyor. He<PERSON>f <PERSON>lığınızı değiştirmeniz mi gerekiyor? 'lib' derleyici seçeneğini '{1}' veya üzeri olarak değiştirmeyi deneyin.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "'{0}' ad<PERSON> bulunamıyor. <PERSON><PERSON><PERSON>ğınızı değiştirmeniz gerekiyor mu? 'lib' derley<PERSON> seçeneğini 'dom' içerecek şekilde değiştirmeyi deney<PERSON>.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "'{0}' adı bulunamıyor. Bun için tür tanımlarını yüklemeniz mi gerekiyor? `npm i --save-dev @types/bun` deneyin.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "'{0}' adı bulunamıyor. Bun için tür tanımlarını yüklemeniz mi gerekiyor? 'npm i --save-dev @types/bun' komutunu deneyin ve ardından tsconfig dosyanızdaki type alanına 'bun' ekleyin.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "'{0}' adı bulunamıyor. Test Runner i<PERSON><PERSON> tür tanı<PERSON>ını yüklemeniz mi gerekiyor? Şunları deneyin: `npm i --save-dev @types/jest` veya `npm i --save-dev @types/mocha`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "'{0}' ad<PERSON> bulunamıyor. Test Runner i<PERSON><PERSON> tür tanımlarını yüklemeniz mi gerekiyor? Şunları deneyin: `npm i --save-dev @types/jest` veya `npm i --save-dev @types/mocha`. Ardından tsconfig dosyanızdaki türler alanına 'jest' veya 'mocha' ekleyin.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "'{0}' adı bulunamıyor. jQuery için tür tanımlarını yüklemeniz mi gerekiyor? <PERSON><PERSON>u deneyin: `npm i --save-dev @types/jquery`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "'{0}' adı bulunamıyor. jQuery için tür tanımlarını yüklemeniz gerekiyor mu? <PERSON>unu deneyin: `npm i --save-dev @types/jquery`. Ardından tsconfig dosyanızdaki türler alanına 'jquery' öğesini ekleyin.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "'{0}' adı bulunamıyor. D<PERSON><PERSON><PERSON>m için tür tanımlarını yüklemeniz mi gerekiyor? <PERSON><PERSON>u deneyin: `npm i --save-dev @types/node`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "'{0}' adı bulunamıyor. <PERSON><PERSON><PERSON><PERSON><PERSON> için tür tanımlarını yüklemeniz mi gerekiyor? <PERSON><PERSON>u deneyin: `npm i --save-dev @types/node`. Ardından tsconfig dosyanızdaki türler alanına 'node' ekleyin.", "Cannot_find_namespace_0_2503": "'{0}' ad alanı bulunamıyor.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Ad alanı '{0}' bulunamıyor. Bunu mu demek istediniz: '{1}'?", "Cannot_find_parameter_0_1225": "'{0}' parametresi bulunamıyor.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "G<PERSON>ş dosyalarına ait ortak alt dizin yolu bulunamıyor.", "Cannot_find_type_definition_file_for_0_2688": "'{0}' i<PERSON><PERSON> tür tanımı dosyası bulunamıyor.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "<PERSON><PERSON>r bildirim dosyaları içeri aktarılamıyor. '{1}' yerine '{0}' dosyasını içeri aktarmanız önerilir.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "<PERSON><PERSON><PERSON> kapsamdaki '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, blok kapsamındaki '{1}' bi<PERSON><PERSON><PERSON><PERSON> aynı kapsamda başlatılamaz.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'null' olan bir nesne çağrılamıyor.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'null' veya 'undefined' olan bir nesne çağrılamıyor.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'undefined' o<PERSON> bir nesne çağrılamıyor.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>in 'next' metodu '{1}' t<PERSON><PERSON><PERSON><PERSON><PERSON> bekle<PERSON>ine ra<PERSON><PERSON> dizi bozma her zaman '{0}' gönderdiğinden değer yinelenemiyor.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>in 'next' metodu '{1}' t<PERSON><PERSON><PERSON><PERSON><PERSON> beklemesine rağmen dizi yayılması her zaman '{0}' gönderdiğinden değer yinelenemiyor.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>in 'next' metodu '{1}' t<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>ine ra<PERSON><PERSON> for-of her zaman '{0}' gönderdiğ<PERSON><PERSON> de<PERSON> y<PERSON>r.", "Cannot_move_statements_to_the_selected_file_95183": "İfadeler seçilen dosyaya taşınamıyor", "Cannot_move_to_file_selected_file_is_invalid_95179": "Dosyaya taşınamıyor, seç<PERSON> dosya geçersiz", "Cannot_read_file_0_5083": "'{0}' dos<PERSON><PERSON> okunamıyor.", "Cannot_read_file_0_Colon_1_5012": "'{0}' dos<PERSON><PERSON> okunamıyor: {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Blok kapsamlı değişken '{0}', yeniden bildirilemiyor.", "Cannot_redeclare_exported_variable_0_2323": "Dışarı aktarılan '{0}' değişkeni yeniden bildirilemiyor.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Catch yan tümcesindeki '{0}' tanımlayıcısı yeniden bildirilemiyor.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Bir tür ek açıklamasında bir işlev çağrısı başlatılamıyor.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "'--jsx' bayrağı sağlanmazsa JSX kullanılamaz.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "'{0}' etkinleş<PERSON>rildiğinde, yalnızca tür veya tür ad alanında 'export import' kullanılamaz.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "'--module' de<PERSON><PERSON> 'none' olduğunda içeri aktarma, dışarı aktarma veya modül genişletme kullanılamaz.", "Cannot_use_namespace_0_as_a_type_2709": "'{0}' ad al<PERSON><PERSON>, tür olarak k<PERSON>.", "Cannot_use_namespace_0_as_a_value_2708": "'{0}' ad <PERSON><PERSON><PERSON>, de<PERSON><PERSON> o<PERSON> k<PERSON>anı<PERSON>.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "'This' ifadesi bir donatılmış sınıfın statik özellik başlatıcısında kullanılamaz.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "'{0}' dos<PERSON><PERSON>, ba<PERSON><PERSON>rulan '{1}' projesi tarafından oluşturulan '.tsbuildinfo' dosyasının üzerine yazacağından bu dosya yazılamıyor", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Birden fazla giriş dosyası tarafından üzerine yazılacağı için '{0}' dosyası yazılamıyor.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "<PERSON><PERSON><PERSON> dos<PERSON>ının üzerine yazacağı için '{0}' dosyası yazılamıyor.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Catch yan tümcesi değişkeni bir başlatıcıya sahip olamaz.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Belirtilmişse catch yan tümcesi değişken türü ek açıklaması 'any ' veya 'unknown' o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Change_0_to_1_90014": "'{0}' de<PERSON><PERSON><PERSON> '{1}' <PERSON><PERSON><PERSON>", "Change_all_extended_interfaces_to_implements_95038": "Tüm genişletilmiş arabirimleri 'implements' o<PERSON><PERSON>", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Tüm jsdoc-style türlerini TypeScript olarak değiştir", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Tüm jsdoc-style türlerini TypeScript olarak değiştir (ve null yapılabilir türlere '| undefined' ekle)", "Change_extends_to_implements_90003": "'extends' if<PERSON><PERSON> 'implements' <PERSON><PERSON><PERSON>", "Change_spelling_to_0_90022": "Ya<PERSON><PERSON>m<PERSON> '{0}' <PERSON><PERSON><PERSON> değiştir", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Bildirilen ancak oluşturucuda ayarlanmamış sınıf özelliklerini denetleyin.", "Check_side_effect_imports_6806": "Yan etki içeri aktarmalarını denetleyin.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "'bind', 'call' ve 'apply' yöntemlerinin bağımsız değişkenlerinin özgün işlevle eşleşip eşleşmediğini denetle.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "'{0}' ön ekinin '{1}' - '{2}' için e<PERSON> en uzun ön ek olup olmadığı denetleniyor.", "Circular_definition_of_import_alias_0_2303": "'{0}' içeri aktarma diğer adının dö<PERSON> tanımı.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Yapılandırma çözümlenirken döngüsellik algılandı: {0}", "Circularity_originates_in_type_at_this_location_2751": "Döngüsellik bu konumdaki türden kaynaklanıyor.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "'{0}' sın<PERSON><PERSON><PERSON>, '{1}' örnek üyesi erişimcisini tanımlar; ancak genişletilmiş '{2}' s<PERSON>n<PERSON><PERSON><PERSON>, bunu bir örnek üyesi işlevi olarak tanımlar.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "'{0}' sın<PERSON><PERSON><PERSON>, '{1}' örnek üyesi işlevini tanımlar; ancak genişletilmiş '{2}' s<PERSON>n<PERSON><PERSON><PERSON>, bunu bir örnek üyesi erişimcisi olarak tanımlar.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "'{0}' sını<PERSON><PERSON>, '{1}' örnek üyesi özelliğini tanımlar; ancak genişletilmiş '{2}' sın<PERSON><PERSON><PERSON>, bunu bir örnek üyesi işlevi olarak tanımlar.", "Class_0_incorrectly_extends_base_class_1_2415": "'{0}' sınıf<PERSON>, '{1}' temel sınıfını yanlış genişletiyor.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "'{0}' sınıfı hatalı olarak '{1}' sınıfını uyguluyor. '{1}' sınıfını genişletip üyelerini bir alt sınıf olarak devralmak mı istiyordunuz?", "Class_0_incorrectly_implements_interface_1_2420": "'{0}' sın<PERSON><PERSON><PERSON>, '{1}' a<PERSON><PERSON><PERSON> yanlış uyguluyor.", "Class_0_used_before_its_declaration_2449": "'{0}' s<PERSON><PERSON><PERSON><PERSON><PERSON>, bildiriminden önce kullanıldı.", "Class_constructor_may_not_be_a_generator_1368": "<PERSON><PERSON><PERSON><PERSON><PERSON>, program yönergeleri üreten bir oluşturucu olamaz.", "Class_constructor_may_not_be_an_accessor_1341": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bir <PERSON><PERSON><PERSON><PERSON> o<PERSON>.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "<PERSON><PERSON><PERSON><PERSON><PERSON>, '{0}' i<PERSON><PERSON> a<PERSON>ı<PERSON><PERSON> yü<PERSON>me listesi uygulayamaz.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "<PERSON><PERSON><PERSON><PERSON><PERSON> bildiri<PERSON>inde birden fazla '@augments' veya '@extends' et<PERSON>ti olamaz.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Sınıf dekoratörleri statik özel tanımlayıcıyla kullanılamaz. Deneysel dekoratörü kaldırmayı düşünün.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "Ana sınıf tarafından tanımlanan '{0}' s<PERSON><PERSON><PERSON><PERSON> al<PERSON>, alt sınıftan super aracılığıyla erişilemez.", "Class_name_cannot_be_0_2414": "<PERSON><PERSON><PERSON><PERSON><PERSON> adı '{0}' olamaz.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Modül {0} ile ES5 hedeflendiğinde sınıf adı 'Object' olamaz.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "'{0}' statik sınıf tarafı, '{1}' statik temel sınıf tarafını yanlış genişletiyor.", "Classes_can_only_extend_a_single_class_1174": "Sınıflar yalnızca bir sınıfı genişletebilir.", "Classes_may_not_have_a_field_named_constructor_18006": "Sınıflarda 'constructor' ad<PERSON><PERSON> bir alan olma<PERSON>.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Sı<PERSON><PERSON>fta bulunan kod, bu '{0}' kullanımına izin vermeyen JavaScript'in katı modunda değerlendirilir. Daha fazla bilgi için bkz. https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Komut Satırı Seçenekleri", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Yapılandırma dos<PERSON>ının yolu veya 'tsconfig.json' dosyasını içeren klasörün yolu belirtilen projeyi derleyin.", "Compiler_Diagnostics_6251": "Derleyici Tanılaması", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "'{0}' der<PERSON><PERSON> se<PERSON> boş bir dize verilemez.", "Compiler_option_0_expects_an_argument_6044": "'{0}' der<PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ğ<PERSON> bekliyor.", "Compiler_option_0_may_not_be_used_with_build_5094": "'--{0}' der<PERSON><PERSON>, '--build' ile k<PERSON>.", "Compiler_option_0_may_only_be_used_with_build_5093": "'--{0}' der<PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> '--build' ile kullanılabilir.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "Derleyici seçeneği '{0}' değeri '{1}' kararsız. Bu hatayı sessize almak için gecelik TypeScript kullanın. 'npm install -D typescript@next' ile güncelleştirmeyi deneyin.", "Compiler_option_0_requires_a_value_of_type_1_5024": "'{0}' <PERSON><PERSON><PERSON>, {1} tür<PERSON>nde bir değer gerektiriyor.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Özel tanımlayıcı alt düzeyi gösterilirken derleyici '{0}' adını ayırır.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Belirtilen yolda bulunan TypeScript projesini derler.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Geç<PERSON><PERSON> (çalışma dizinindeki tsconfig.js).", "Compiles_the_current_project_with_additional_settings_6929": "Geçerli projeyi ek a<PERSON><PERSON>.", "Completeness_6257": "Tamlık", "Composite_projects_may_not_disable_declaration_emit_6304": "<PERSON><PERSON><PERSON><PERSON> proje<PERSON>, bi<PERSON><PERSON><PERSON> gösterimini devre dışı bırak<PERSON>.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Bileşik projeler artımlı derlemeyi devre dışı bırakamayabilir.", "Computed_from_the_list_of_input_files_6911": "G<PERSON>ş dosyaları listesinden hesaplanır", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "Hesaplanan özellikler sayı veya dize değ<PERSON>şmezleri, değişkenler veya --isolatedDeclarations içeren noktalı ifadeler olmalıdır.", "Computed_property_names_are_not_allowed_in_enums_1164": "Sabit listelerinde hesaplanan özellik adına izin verilmiyor.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "<PERSON>ı<PERSON><PERSON>f veya nesne değişmezlerindeki hesaplanan özellik adları --isolatedDeclarations ile çıkarsanamaz.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Dize değeri içeren üyelerin bulunduğu bir sabit listesinde hesaplanan değerlere izin verilmez.", "Concatenate_and_emit_output_to_single_file_6001": "Çıktıyı tek dosyaya birleştirin ve yayın.", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "İçe aktarmaları çözümlerken çözümleyiciye özgü varsayılanlara ek olarak ayarlanacak koşullar.", "Conflicts_are_in_this_file_6201": "Çakışmalar bu dosyada bulunuyor.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Bu sınıfa 'declare' değiştiricisi eklemeyi düşünün.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "'{0}' ve '{1}' yapı imzası dönüş türleri uyumlu değil.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Dönüş türü ek açıklaması bulunmayan yapı imzası, örtük olarak 'any' dönüş türüne sahip.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Bağımsız değişken içermeyen yapı imzaları uyumsuz '{0}' ve '{1}' dönüş türlerine sahip.", "Constructor_implementation_is_missing_2390": "Oluşturucu uygulaması yok.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "'{0}' s<PERSON><PERSON><PERSON><PERSON>ının oluşturucusu özel olduğundan, oluşturucuya yalnızca sınıf bildiriminden erişilebilir.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "'{0}' sın<PERSON>fının oluşturucusu korumalı olduğundan, oluşturucuya yalnızca sınıf bildiriminden erişilebilir.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "Oluşturucu türü gösterimi bir birleşim türünde kullanıldığında parantez içine alınmalıdır.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "Oluşturucu türü gösterimi bir kesişim türünde kullanıldığında parantez içine alınmalıdır.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Türetilmiş sınıflara ilişkin oluşturucular bir 'super' çağrısı içermelidir.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Kapsayıcı dosya belirtilmedi ve kök dizini belirlenemiyor; 'node_modules' klasöründe arama atlanıyor.", "Containing_function_is_not_an_arrow_function_95128": "İçeren işlev bir ok işlevi değil", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Modül biçimli JS dosyalarını algılamak için kullanılan yöntemi denetle.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "Türler birbiriyle yeterince örtüşmediğinden '{0}' türünün '{1}' türüne dönüştürülmesi bir hata olabilir. Bu bilerek yapıldıysa, ifadeyi önce 'unknown' de<PERSON><PERSON><PERSON> dö<PERSON>ürü<PERSON>.", "Convert_0_to_1_in_0_95003": "'{0}' ö<PERSON><PERSON><PERSON> '{0} içinde {1}' ö<PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>", "Convert_0_to_mapped_object_type_95055": "'{0}' öğ<PERSON>ni e<PERSON>lenen nesne türüne dönüştür", "Convert_all_const_to_let_95102": "Tü<PERSON> 'const' if<PERSON><PERSON><PERSON> 'let' if<PERSON><PERSON> dö<PERSON><PERSON><PERSON><PERSON><PERSON>", "Convert_all_constructor_functions_to_classes_95045": "Tüm oluşturucu işlevleri sınıflara dönüştür", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Tüm geçersiz karakterleri HTML varlık koduna dönüştür", "Convert_all_re_exported_types_to_type_only_exports_1365": "Yeniden dışarı aktarılan tüm türleri yalnızca tür dışarı aktarmalarına dönüştürün", "Convert_all_require_to_import_95048": "Tüm 'require' <PERSON><PERSON><PERSON><PERSON> 'import' o<PERSON><PERSON> dönüştür", "Convert_all_to_async_functions_95066": "Tümünü asenkron işlevlere dönüştürün", "Convert_all_to_bigint_numeric_literals_95092": "Tümünü büyük tamsayı sayısal sabit değerlerine dönüştürün", "Convert_all_to_default_imports_95035": "Tümünü <PERSON>sayılan içeri aktarmalara dönüştür", "Convert_all_type_literals_to_mapped_type_95021": "Tüm tür sabit değerlerini eşlenmiş türe dönüştür", "Convert_all_typedef_to_TypeScript_types_95177": "Tüm typedef'leri TypeScript türüne dönüştür.", "Convert_arrow_function_or_function_expression_95122": "Ok işlevini veya işlev ifadesini dönüştür", "Convert_const_to_let_95093": "'const' if<PERSON><PERSON> 'let' if<PERSON><PERSON> d<PERSON>", "Convert_default_export_to_named_export_95061": "Varsayılan dışarı aktarmayı adlandırılmış dışarı aktarmaya dönüştürün", "Convert_function_declaration_0_to_arrow_function_95106": "'{0}' <PERSON><PERSON><PERSON> bild<PERSON> ok işlevine dönüştür", "Convert_function_expression_0_to_arrow_function_95105": "'{0}' <PERSON><PERSON><PERSON> ok işlevine dönüştür", "Convert_function_to_an_ES2015_class_95001": "İşlevi bir ES2015 sınıfına dönüştür", "Convert_invalid_character_to_its_html_entity_code_95100": "Geçersiz karakteri, karakterin HTML varlık koduna dönüştürün", "Convert_named_export_to_default_export_95062": "Adlandırılmış dışarı aktarmayı varsayılan dışarı aktarmaya dönüştürün", "Convert_named_imports_to_default_import_95170": "Adlandırılmış içeri aktarmaları varsayılan içeri aktarmaya dönüştür", "Convert_named_imports_to_namespace_import_95057": "Adlandırılmış içeri aktarmaları ad alanı içeri aktarmasına dönüştür", "Convert_namespace_import_to_named_imports_95056": "Ad alanı içeri aktarmasını adlandırılmış içeri aktarmalara dönüştür", "Convert_overload_list_to_single_signature_95118": "Aşırı yükleme listesini tek imzaya dönüştür", "Convert_parameters_to_destructured_object_95075": "<PERSON><PERSON><PERSON><PERSON>, bozulan nes<PERSON>e dö<PERSON>ü<PERSON>ü<PERSON>ü<PERSON>", "Convert_require_to_import_95047": "'require' öğesini 'import' olarak dönüştür", "Convert_to_ES_module_95017": "ES modülüne dönüştür", "Convert_to_a_bigint_numeric_literal_95091": "Büyük tamsayı sayısal sabit değerine dönüştürün", "Convert_to_anonymous_function_95123": "<PERSON><PERSON><PERSON> dönüştür", "Convert_to_arrow_function_95125": "Ok işlevine dönüştür", "Convert_to_async_function_95065": "Asenkron işleve dönüştürün", "Convert_to_default_import_95013": "Varsayılan içeri aktarmaya dönüştür", "Convert_to_named_function_95124": "Adlandırılmış işleve dönüştür", "Convert_to_optional_chain_expression_95139": "İsteğe bağlı zincir ifadesine dönüştür", "Convert_to_template_string_95096": "Şablon dizesine dö<PERSON>", "Convert_to_type_only_export_1364": "Yalnızca tür dışarı aktarmaya dönüştürün", "Convert_typedef_to_TypeScript_type_95176": "typedef'i TypeScript türüne dönüştürün.", "Corrupted_locale_file_0_6051": "{0} yerel ayar dos<PERSON> bozuk.", "Could_not_convert_to_anonymous_function_95153": "<PERSON><PERSON><PERSON> i<PERSON> dönüştürülemedi", "Could_not_convert_to_arrow_function_95151": "Arrow işlevine dönüştürülemedi", "Could_not_convert_to_named_function_95152": "Adlandırılmış işleve dönüştürülemedi", "Could_not_determine_function_return_type_95150": "<PERSON><PERSON><PERSON> d<PERSON> türü belirlene<PERSON>i", "Could_not_find_a_containing_arrow_function_95127": "İçeren bir ok işlevi bulunamadı", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "'{0}' m<PERSON><PERSON><PERSON><PERSON> için bildirim dosyası bulunamadı. '{1}' ört<PERSON><PERSON><PERSON> o<PERSON> 'any' tür<PERSON><PERSON> sahip.", "Could_not_find_convertible_access_expression_95140": "Dönüştürülebilir erişim ifadesi bulunamadı", "Could_not_find_export_statement_95129": "Dışarı aktarma ifadesi bulunamadı", "Could_not_find_import_clause_95131": "İçeri aktarma yan tümcesi bulunamadı", "Could_not_find_matching_access_expressions_95141": "Eşleşen eri<PERSON>im <PERSON>adeleri bulunamadı", "Could_not_find_name_0_Did_you_mean_1_2570": "'{0}' adı bulunamıyor. Şunu mu demek istediniz: '{1}'?", "Could_not_find_namespace_import_or_named_imports_95132": "Ad alanı içeri aktarması veya adlandırılmış içeri aktarmalar bulunamadı", "Could_not_find_property_for_which_to_generate_accessor_95135": "Erişimcinin oluşturulacağı özellik bulunamadı", "Could_not_find_variable_to_inline_95185": "Satır içi değişken bulunamadı.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Uzantılara sahip '{0}' <PERSON><PERSON> çözümlenemedi: {1}.", "Could_not_write_file_0_Colon_1_5033": "'{0}' dos<PERSON><PERSON> ya<PERSON>ılamadı: {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Yayılan JavaScript dosyaları için kaynak eşleme dosyaları oluşturun.", "Create_sourcemaps_for_d_ts_files_6614": "d.ts dosyaları için kaynak eşlemeleri oluşturun.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Çalışma dizininde önerilen ayarlarla ilgili bir tsconfig.js oluşturur.", "DIRECTORY_6038": "DİZİN", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "Bir karakter sınıfında ondalık kaçış dizilerine ve geri referanslara izin verilmez.", "Decimals_with_leading_zeros_are_not_allowed_1489": "Başında sıfır bulunan ondalık sayılara izin verilmez.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "B<PERSON><PERSON>im başka bir dosyadaki bildirimi genişlettiğinden serileştirilemez.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "Bu dosyaya ili<PERSON> bildirim, bu içe aktarmanın genişletmeler için korunmasını gerektirir. Bu, --isolatedDeclarations ile desteklenmez.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Bu dosya için bildirim gösterme, '{0}' özel adını kullanmayı gerektiriyor. Açık tür ek açıklaması, bildirim gösterme engelini kaldırabilir.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Bu dosya için bildirim gösterme, '{1}' modülündeki '{0}' özel adını kullanmayı gerektiriyor. Açık tür ek açıklaması, bildirim gösterme engelini kaldırabilir.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "Bu parametre için bildiri<PERSON>, tür<PERSON><PERSON> ö<PERSON>ülü olarak tanımsız eklenmesini gerektirir. Bu, --isolatedDeclarations ile desteklenmez.", "Declaration_expected_1146": "<PERSON><PERSON><PERSON><PERSON>.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "<PERSON><PERSON><PERSON><PERSON>, yer<PERSON><PERSON><PERSON> genel tanımlayıcı '{0}' ile <PERSON>şıyor.", "Declaration_or_statement_expected_1128": "<PERSON><PERSON><PERSON><PERSON> veya deyim bekle<PERSON>.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Bildirim veya deyim bekleniyor. Bu '=' bir ifade bloğunun ardından geldiğinden yapı çözümlü bir atama yazmayı düşünüyorsanız, tüm atamayı parantez içine almanız gerekebilir.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "<PERSON><PERSON> atama onaylamaları olan bildirimlerde tür ek açıklamaları da olmalıdır.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Başlatıcılara sahip bildirimlerde kesin atama onaylamaları olamaz.", "Declare_a_private_field_named_0_90053": "'{0}' ad<PERSON><PERSON> bir <PERSON>zel alan bild<PERSON>n.", "Declare_method_0_90023": "'{0}' metod<PERSON>u bildir", "Declare_private_method_0_90038": "'{0}' <PERSON><PERSON> metodunu bildirin.", "Declare_private_property_0_90035": "Özel '{0}' özelliğini bildir", "Declare_property_0_90016": "'{0}' özelliğini bildir", "Declare_static_method_0_90024": "'{0}' statik metodunu bildir", "Declare_static_property_0_90027": "'{0}' statik özelliğini bildir", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "'{0}' dekoratör işlevi dö<PERSON> türü, '{1}' tü<PERSON><PERSON><PERSON> atanamıyor.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Dekoratör işlevi dönüş türü '{0}', ancak 'void' veya 'any' olma<PERSON><PERSON> bekleniyor.", "Decorator_used_before_export_here_1486": "Dekoratör burada 'export' öğesinden önce kullanıldı.", "Decorators_are_not_valid_here_1206": "Buradaki dekoratörler geçerli değil.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Dekoratörler aynı ada sahip birden fazla get/set eri<PERSON><PERSON><PERSON>ine uygulanamaz.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "Dekoratörler 'export' öncesinde de görünüyorlarsa, 'export' veya 'default export' sonrasında görünmeyebilirler.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, özellik bildirimlerinin adından ve tüm anahtar sözcüklerinden önce gelmelidir.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "Catch yan tümcesi değişkenlerini varsayılan olarak 'any' yerine 'unknown' olarak kabul et.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Modülün var<PERSON>ılan dışarı aktarımı '{0}' özel adına sahip veya bu adı kullanıyor.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "Varsayılan dışa aktarımlar --isolatedDeclarations ile çıkarsanamaz.", "Default_library_1424": "Varsayılan kitaplık", "Default_library_for_target_0_1425": "'{0}' he<PERSON><PERSON>lık", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "<PERSON><PERSON> tanımlayıc<PERSON>ların tanımları başka bir dosyadaki tanımlarla çakışıyor: {0}", "Delete_all_unused_declarations_95024": "<PERSON><PERSON><PERSON><PERSON><PERSON> tüm bildirimleri sil", "Delete_all_unused_imports_95147": "Kullanılmayan tüm içeri aktarmaları sil", "Delete_all_unused_param_tags_95172": "<PERSON><PERSON><PERSON><PERSON><PERSON> tüm “@param” etiketlerini silin", "Delete_the_outputs_of_all_projects_6365": "<PERSON>ü<PERSON> projelerin çıktılarını sil.", "Delete_unused_param_tag_0_95171": "<PERSON><PERSON><PERSON><PERSON><PERSON> “{0}” “@param” etiket adını silin", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[<PERSON><PERSON><PERSON><PERSON>] Bunun yerine '--jsxFactory' kullanın. 'react' JSX gösterimi hedefleni<PERSON>, createElement için çağrılan nesneyi belirtin", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[<PERSON><PERSON><PERSON><PERSON>] Bunun yerine '--outF<PERSON>' kullanın. Çıkışı tek bir dosya olarak birleştirin ve gösterin", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[<PERSON><PERSON><PERSON><PERSON>] Bunun yerine '--skipLibCheck' kullanın. Varsayılan kitaplık bildirim dosyalarının tür denetimini atlayın.", "Deprecated_setting_Use_outFile_instead_6677": "<PERSON><PERSON> kullanım dışı bırakıldı. <PERSON><PERSON><PERSON> yerine 'outFile' kullanın.", "Did_you_forget_to_use_await_2773": "'await' k<PERSON><PERSON><PERSON><PERSON>ı mı unuttunuz?", "Did_you_mean_0_1369": "<PERSON><PERSON>u mu demek istediniz: '{0}'?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "'{0}' değerinin 'new (...args: any[]) => {1}' tür<PERSON>ne kısıtlanmasını mı istediniz?", "Did_you_mean_to_call_this_expression_6212": "Bu ifadeyi mi çağırmak istediniz?", "Did_you_mean_to_mark_this_function_as_async_1356": "<PERSON>u i<PERSON><PERSON><PERSON> 'async' olarak işaretlemek mi istediniz?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "':' kullanmak mı istediniz? İçeren nesne sabit değeri, yok etme deseninin parçası olduğunda özellik adının ardından yalnızca '=' gelebilir.", "Did_you_mean_to_use_new_with_this_expression_6213": "Bu ifadeyle 'new' kullanmak mı istediniz?", "Digit_expected_1124": "<PERSON><PERSON><PERSON> be<PERSON>.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "'{0}' di<PERSON><PERSON> yo<PERSON>, içindeki tüm aramalar atlanıyor.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "'{0}' dizini bir package.json kapsamı içermiyor. İçeri aktarmalar çözümlenmeyecek.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Yayılan JavaScript dosyalarında ‘use strict’ yönergelerini eklemeyi devre dışı bırakın.", "Disable_checking_for_this_file_90018": "Bu dosya için denetimi devre dışı bırak", "Disable_emitting_comments_6688": "Yorumların yayılmasını devre dışı bırakın.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "JSDoc açıklamalarında '@internal' olan üretme bildirimlerini devre dışı bırak.", "Disable_emitting_files_from_a_compilation_6660": "Derlemeden dosya yayımlamayı devre dışı bırakın.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "<PERSON><PERSON><PERSON> bir tür denetimi hatası bild<PERSON>i<PERSON>e, dosya yaymayı devre dışı bırakın.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodda 'const enum' bildiri<PERSON><PERSON>n silinmesini devre dışı bırak.", "Disable_error_reporting_for_unreachable_code_6603": "Ulaşılamaz kod için hata raporlamayı devre dışı bırakın.", "Disable_error_reporting_for_unused_labels_6604": "Kullanılmayan etiketler için hata raporlamayı devre dışı bırakın.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "<PERSON> tür denetimini devre dışı bırakın (yalnızca kritik ayrıştırma ve yayma hataları rapor edilecektir).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Derlenen çıktıda '__extends' gibi özel yardımcı işlevler oluşturmayı devre dışı bırak.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Varsayılan lib.d.ts dahil olmak üzere kitaplık dosyalarının dahil edilmesini devre dışı bırakın.", "Disable_loading_referenced_projects_6235": "Başvurulan projelerin yüklenmesini devre dışı bırakın.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Bileşik projelere başvurulurken bildirim dosyaları yerine kaynak dosyaların tercih edilmesini devre dışı bırak.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Nesne sabit değerleri oluşturulurken fazlalık özellik hatalarının raporlanmasını devre dışı bırakın.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Simgesel bağlantıları gerçek yollarına çözümlemeyi devre dışı bırakın. B<PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aynı bayrakla bağıntılıdır.", "Disable_size_limitations_on_JavaScript_projects_6162": "JavaScript projelerinde boyut sınırlamalarını devre dışı bırakın.", "Disable_solution_searching_for_this_project_6224": "Bu proje için çözüm aramayı devre dışı bırakın.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "<PERSON><PERSON><PERSON> türlerinde genel imzalar için katı denetimi devre dışı bırakın.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "JavaScript projeleri için tür alımını devre dışı bırak", "Disable_truncating_types_in_error_messages_6663": "Hata iletilerinde türlerin kesilmesini devre dışı bırakın.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Başvurulan projelerdeki bildirim dosyaları yerine kaynak dosyalarının kullanımını devre dışı bırakın.", "Disable_wiping_the_console_in_watch_mode_6684": "İzleme modunda konsolu temizlemeyi devre dışı bırak.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Bir projedeki dosya adlarına bakarak tür alımı çıkarımı devre dışı bırakır.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "'import', 'require' veya '<reference>' ifadelerinin TypeScript'in projeye eklemesi gereken dosya sayısını artırmasına izin verme.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Aynı dosyaya yönelik tutarsız büyük/küçük harflere sahip başvurulara izin verme.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Derlenen dosya listesine üç eğik çizgi başvuruları veya içeri aktarılan modüller eklemeyin.", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "ECMAScript'in parçası olmayan çalışma zamanı yapılarına izin verme.", "Do_not_emit_comments_to_output_6009": "Çıktıya ait açıklamaları gösterme.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "'@internal' ek açıklamasına sahip kod için bildirimleri gösterme.", "Do_not_emit_outputs_6010": "Çıktıları gösterme.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Her<PERSON><PERSON> bir hata bildirildiyse çıkışları gösterme.", "Do_not_emit_use_strict_directives_in_module_output_6112": "Modül çı<PERSON>ışında 'use strict' yönergeleri gösterme.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Oluşturulan kodda const sabit listesi bildirimlerini silme.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Derlenen çıkışta '__extends' gibi özel yardımcı işlevler oluşturmayın.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Varsayılan kitaplık dosyasını (lib.d.ts) eklemeyin.", "Do_not_report_errors_on_unreachable_code_6077": "Erişilemeyen kod ile ilgili hataları bildirme.", "Do_not_report_errors_on_unused_labels_6074": "Kullanılmayan etiketler ile ilgili hataları bildirme.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Simgesel bağlantıların gerçek yolunu çözümlemeyin.", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "'module' a<PERSON><PERSON><PERSON> göre çıktı dosyasının formatında yazılmalarını sağlayarak, yalnızca tür olarak işaretlenmemiş hiçbir bir içe aktarma veya dışa aktarma işlemini dönüştürmeyin veya silmeyin.", "Do_not_truncate_error_messages_6165": "<PERSON>a iletilerini kesmeyin.", "Duplicate_function_implementation_2393": "Yinelenen işlev uygulaması.", "Duplicate_identifier_0_2300": "Yinelenen tanımlayıcı: '{0}'.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Yinelenen tanımlayıcı: '{0}'. <PERSON><PERSON><PERSON>, bir modü<PERSON>ün üst düzey kapsamındaki '{1}' adın<PERSON> ayırır.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "'{0}' <PERSON><PERSON><PERSON><PERSON>ıcısı yineleniyor. <PERSON><PERSON><PERSON>, zaman uyumsuz işlevler içeren bir modülün en üst düzey kapsamında '{1}' adını ayırıyor.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>cısı yineleniyor. <PERSON><PERSON><PERSON>, statik başlatıcılarda 'super' başvurularını yayımlarken '{1}' adını ayırır.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Yinelenen tanımlayıcı: '{0}'. <PERSON><PERSON><PERSON>, zaman uyumsuz işlevleri desteklemek için '{1}' bild<PERSON><PERSON> kullanır.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "Yinelenen tanımlayıcı: '{0}'. Statik öğeler ve örnek öğeleri aynı özel adı paylaşamaz.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Yinelenen tanımlayıcı: 'arguments'. <PERSON><PERSON><PERSON>, rest parametrelerini başlatmak için 'arguments' tanımlayıcısını kullanır.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Yinelenen '_newTarget' tanımlayıcısı. Derleyicide, '_newTarget' değişken bildirimi 'new.target' meta-özellik başvurusu yakalamak için kullanılıyor.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "<PERSON><PERSON><PERSON> tanımlayıcı: '_this'. <PERSON><PERSON><PERSON>, 'this' ba<PERSON><PERSON><PERSON><PERSON>u yakalamak için '_this' değiş<PERSON> bildirimini kullanır.", "Duplicate_index_signature_for_type_0_2374": "'{0}' tü<PERSON><PERSON> i<PERSON>in yinelenen dizin imzası var.", "Duplicate_label_0_1114": "'{0}' et<PERSON><PERSON> y<PERSON>.", "Duplicate_property_0_2718": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>.", "Duplicate_regular_expression_flag_1500": "Yinelenen normal ifade bayrağı.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Dinamik içeri aktarmanın tanımlayıcısı 'string' tür<PERSON>nde olmalıdır, ancak buradaki tür: '{0}'.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Dinamik içeri aktarma yalnızca '--module' bayrağı 'es2020', 'es2022', 'esnext', 'commonjs', 'amd', 'system', 'umd', 'node16', 'node18' veya 'nodenext' olarak ayarlandığında desteklenir.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "Dinamik içe aktarmalar yalnızca bir modül belirticiyi ve isteğe bağlı bir dizi özniteliği bağımsız değişken olarak kabul edebilir", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "Dinamik içeri aktarmalar yalnızca '--module' se<PERSON><PERSON><PERSON><PERSON>; 'esnext', 'node16', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında ikinci bir bağımsız değişkeni destekler.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "'Module', 'preserve' olarak ayarlandığında CommonJS modülünde ESM sözdizimine izin verilmez.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "'verbatimModuleSyntax' etkinleştirildiğinde CommonJS modülünde ESM söz dizimi kullanılamaz.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "'{0}.{1}' <PERSON><PERSON><PERSON> her bildirim, de<PERSON><PERSON><PERSON> far<PERSON>lılı<PERSON> gösteriyor, '{2}' beklenirken '{3}' verildi.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "'{0}' <PERSON><PERSON><PERSON><PERSON> tü<PERSON>n her bir üyesi yapı imzalarına sahip ancak bu imzaların hiçbiri birbiriyle uyumlu değil.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "'{0}' <PERSON><PERSON><PERSON><PERSON> tü<PERSON>n her bir üyesi imzalara sahip ancak bu imzaların hiçbiri birbiriyle uyumlu değil.", "Editor_Support_6249": "Düzenleyici Desteği", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "'{0}' tür<PERSON><PERSON><PERSON> ifade '{1}' tür<PERSON><PERSON><PERSON>n dizinini oluşturmak için kullanılamadığından öğe, örtük olarak 'any' tür<PERSON>ne sahip.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "<PERSON>zin ifadesi 'number' tür<PERSON><PERSON> olmadığından, öğe örtük olarak 'any' türü içeriyor.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "'{0}' tü<PERSON><PERSON><PERSON><PERSON><PERSON> dizin imzası olmadığından öğe dolaylı olarak 'any' tür<PERSON>ne sahip.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "'{0}' tür<PERSON><PERSON><PERSON><PERSON> dizin imzası olmadığından öğe, örtük olarak 'any' türüne sahip. '{1}' türünü mü çağırmak istediniz?", "Emit_6246": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "ECMAScript-standard-compliant sınıf alanlarını yayın.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Çıkış dosyalarının başında bir UTF-8 Bayt Sırası İşareti (BOM) gösterin.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Ayrı bir dosya oluşturmak yerine, kaynak eşlemeleri içeren tek bir dosya gösterin.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Hata ayıklama için derleyici çalıştırmasının bir v8 CPU profilini yayın.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "CommonJS modüllerini içeri aktarma desteğini kolaylaştırmak için ek JavaScript üret. Bu, tür uyumluluğu için 'allowSyntheticDefaultImports' özelliğini etkinleştirir.", "Emit_class_fields_with_Define_instead_of_Set_6222": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>ını Set yerine Define ile gösterin.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Kaynak dosyalarındaki donatılmış bildirimler için design-type meta verilerini yayın.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "<PERSON>eleme i<PERSON>, ancak ayrıntılı ve daha düşük performanslı JavaScript yayın.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "<PERSON><PERSON><PERSON><PERSON>, kaynak eşlemeleri ile birlikte tek bir dosya içinde gösterin; '--inlineSourceMap' veya '--sourceMap' öğesinin ayarlanmasını gerektirir.", "Enable_all_strict_type_checking_options_6180": "<PERSON>ü<PERSON> katı tür denetleme seçeneklerini etkinleştirin.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Derleyici hatalarının okunmasını kolaylaştırmak için TypeScript çıktısında renk ve biçimlendirmeyi etkinleştirin.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Bir TypeScript projesinin proje başvurularıyla birlikte kullanılmasına olanak sağlayan kısıtlamaları etkinleştirin.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Açıkça bir işlev döndürmeyen kod yolları için hata raporlamayı etkinleştirin.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Örtük olarak 'any' tür<PERSON>ne sahip ifade ve bildirimlerde hata raporlamayı etkinleştir.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Switch deyimlerinde sonraki ifadelere geçiş ile ilgili hataların raporlanmasını etkinleştirin.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "T<PERSON>r denetimli JavaScript dosyalarında hata raporlamayı etkinleştirin.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "<PERSON><PERSON> okunmadığında hata raporlamayı etkinleştir.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "'this' için 'any' türü verildiğinde hata raporlamayı etkinleştir.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "Eski deneysel dekoratörler için deneysel desteği etkinleştirin.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "<PERSON>ir bildirim dos<PERSON>ının mevcut olması koşuluyla, her<PERSON><PERSON> bir uzantıya sahip dosyaların içe aktarılmasını etkinleştirin.", "Enable_importing_json_files_6689": ".json dosyalarını içeri aktarmayı etkinleştirin.", "Enable_lib_replacement_6808": "Kitaplık değişimini etkinleştir.", "Enable_project_compilation_6302": "<PERSON><PERSON>", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "İşlevlerde katı 'bind', 'call' ve 'apply' metotlarını etkinleştirin.", "Enable_strict_checking_of_function_types_6186": "<PERSON><PERSON>lev türleri üzerinde katı denetimi etkinleştirin.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Sınıflarda sıkı özellik başlatma denetimini etkinleştirin.", "Enable_strict_null_checks_6113": "<PERSON><PERSON> <PERSON> denetimlerini etkinleştir.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Yapılandırma <PERSON> 'experimentalDecorators' seçeneğini etkinleştirin", "Enable_the_jsx_flag_in_your_configuration_file_95088": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '--jsx' bayrağını etkinleştirin", "Enable_tracing_of_the_name_resolution_process_6085": "Ad çözümleme işlemini izlemeyi etkinleştir.", "Enable_verbose_logging_6713": "Ayrıntılı günlüğe yazmayı etkinleştir.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Tüm içeri aktarma işlemleri için ad alanı nesnelerinin oluşturulması aracılığıyla CommonJS ile ES Modülleri arasında yayımlama birlikte çalışabilirliğine imkan tanır. Şu anlama gelir: 'allowSyntheticDefaultImports'.", "Enables_experimental_support_for_ES7_decorators_6065": "ES7 dekoratörleri için deney<PERSON> desteği etkinleştirir.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Dekoratörlere tür meta verisi gönderme için deneysel desteği etkinleştirir.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Dizini oluşturulmuş bir tür kullanılarak bildirilen anahtarlar için dizini oluşturulmuş erişimciler kullanılmasını zorunlu kılar.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Türetilmiş sınıflarda geçersiz kılan üyelerin bir geçersiz kılma değiştiricisiyle işaretlendiğinden emin olun.", "Ensure_that_casing_is_correct_in_imports_6637": "İçeri aktarmalarda büyük harfe çevirmenin doğru olduğundan emin olun.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Her dosyanın diğer içeri aktarmalara bağlı olmadan güvenli bir şekilde kaynaktan kaynağa derlenebildiğinden emin olun.", "Ensure_use_strict_is_always_emitted_6605": "'use strict' <PERSON><PERSON><PERSON><PERSON> her zaman yayıldığından emin olun.", "Entering_conditional_exports_6413": "<PERSON><PERSON><PERSON>u dışarı aktarmalara giriliyor.", "Entry_point_for_implicit_type_library_0_1420": "'{0}' örtük tür kitaplığı için giriş noktası", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "'{1}' paket kim<PERSON> sahip '{0}' örtük tür kitaplığı için giriş noktası", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "compilerOptions içinde belirtilen '{0}' tür <PERSON>ı<PERSON>ın giriş noktası", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "'{1}' paket kim<PERSON> sahip compilerOptions içinde belirtilen '{0}' tür kit<PERSON>ı<PERSON>ın giriş noktası", "Enum_0_used_before_its_declaration_2450": "'{0}' sabit listesi, bildiriminden önce kullanıldı.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Sabit listesi bildirimleri yalnızca ad alanı veya diğer sabit listesi bildirimleri ile birleştirilebilir.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Sabit listesi bildirimlerinin tümü const veya const o<PERSON><PERSON> de<PERSON><PERSON> olmalıdır.", "Enum_member_expected_1132": "Sabit listesi ü<PERSON> be<PERSON>.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "Değişmez sayısal bir üyeyi takip eden numaralandırma üyesi, 'isolatedModules' etkinleştirildiğinde bir başlatıcıya sahip olmalıdır.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "Sabit listesi üyesi başlatıcıları, --isolatedDeclarations ile dış simgelere başvurular olmadan hesaplanabilir olmalıdır.", "Enum_member_must_have_initializer_1061": "Sabit listesi üyesi bir başlatıcıya sahip olmalıdır.", "Enum_name_cannot_be_0_2431": "Sabit listesi adı '{0}' olamaz.", "Errors_Files_6041": "Hata Dosyaları", "Escape_sequence_0_is_not_allowed_1488": "'{0}' ka<PERSON><PERSON>ş dizisine izin verilmiyor.", "Examples_Colon_0_6026": "Örnekler: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "'{0}' ve '{1}' tü<PERSON><PERSON> ka<PERSON>laştırırken aşırı karmaşıklık.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>ı<PERSON><PERSON><PERSON>, '{0}' ve '{1}' tü<PERSON><PERSON> kar<PERSON>laştırıyor.", "Exiting_conditional_exports_6416": "Ko<PERSON>ullu dışarı aktarmalardan çıkılıyor.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "{0}-{1} tür ba<PERSON><PERSON><PERSON><PERSON>z değişkeni bekleniyordu; bunları bir '@extends' etiketiyle sa<PERSON>layın.", "Expected_0_arguments_but_got_1_2554": "{0} bağımsız değişken bekleniyordu ancak {1} alındı.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "{0} bağımsız değişken bekleniyordu ancak {1} bağımsız değişken alındı. Tür bağımsız değişkeninizdeki 'void' operatörünü 'Promise'e eklemeyi mi unuttunuz?", "Expected_0_type_arguments_but_got_1_2558": "{0} tür ba<PERSON><PERSON><PERSON><PERSON>z değişkeni bekleniyordu ancak {1} alındı.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "{0} tür bağ<PERSON>ms<PERSON>z değişkeni bekleniyordu; bunları bir '@extends' etiketiyle sağlayın.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "1 bağımsız değişken bekleniyordu ancak 0 bağımsız değişken var. Bağımsız değişkenler olmadan çağrılabilen bir 'resolve' oluşturmak için 'new Promise()' çağrısında bir JSDoc ipucu bulunması gerekir.", "Expected_a_Unicode_property_name_1523": "Unicode özellik adı bekleniyordu.", "Expected_a_Unicode_property_name_or_value_1527": "Unicode özellik adı veya değeri bekleniyordu.", "Expected_a_Unicode_property_value_1525": "Bir Unicode özellik değeri bekleniyordu.", "Expected_a_capturing_group_name_1514": "Yakalama grubu adı bekleniyordu.", "Expected_a_class_set_operand_1520": "Bir sınıf kümesi işleneni bekleniyordu.", "Expected_at_least_0_arguments_but_got_1_2555": "En az {0} bağımsız değişken bekleniyordu ancak {1} alındı.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "'{0}' <PERSON><PERSON><PERSON> ilgili JSX kapanış etiketi bekleniyor.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "JSX parçasına karşılık gelen kapanış etiketi bekleniyordu.", "Expected_for_property_initializer_1442": "Özellik başlatıcısı için '=' bekleniyor.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "'package.json' dosyasındaki '{0}' alanının '{1}' türünde olması bekleniyordu ancak '{2}' alındı.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "<PERSON><PERSON><PERSON>k olarak belirtilen modül çözümleme türü: '{0}'.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "'target' seçeneği 'es2016' veya üzeri olarak belirlenmedikçe 'bigint' değerlerinde üs olarak gösterme yapılamaz.", "Export_0_from_module_1_90059": "'{1}' modülünden '{0}' öğesini dışarı aktar", "Export_all_referenced_locals_90060": "Başvurulan tüm yerel ayarları dışarı aktar", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "ECMAScript modülleri hedeflenirken dışarı aktarma ataması kullanılamaz. Bunun yerine 'export default' veya başka bir modül biçimi kullanmayı deneyin.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "'--module' bayrağ<PERSON> 'system' ise dışarı aktarma ataması desteklenmez.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Dışarı aktarma bildirimi, dışarı aktarılan '{0}' bildirimiyle çakışıyor.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Ad alanında dışarı aktarma bildirimlerine izin verilmez.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "'{0}' dışarı aktarma tanımlayıcısı, '{1}' yolundaki package.json kapsamında yok.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "Dışarı aktarılan '{0}' tür diğer adı, '{1}' özel adına sahip veya bu adı kullanıyor.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "Dışarı aktarılan türün diğer adı olan '{0}' ifadesi, {2} modülündeki '{1}' özel adına sahip veya bu özel adı kullanıyor.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Dışarı aktarılan '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Dışarı aktarılan '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>i, '{2}' özel modüldeki '{1}' adına sahip veya bu adı kullanıyor.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Dışarı aktarılan '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>i, '{1}' özel adına sahip veya bu adı kullanıyor.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Modül genişletmelerinde dışarı aktarmalara ve dışarı aktarma atamalarına izin verilmez.", "Expression_expected_1109": "İfade bekleniyor.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "İfadenin dekoratör olarak kullanılabilmesi için parantez içine alınması gereklidir.", "Expression_or_comma_expected_1137": "İfade veya virgül bekleniyor.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "İ<PERSON><PERSON>, temsil edilemeyecek kadar büyük olan bir demet türü oluşturuyor.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "İfade, temsili çok karmaşık olan bir birleşim türü oluşturuyor.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "<PERSON><PERSON><PERSON>, derleyicinin temel sınıf başvurusunu yakalamak için kullandığı '_super' öğesi olarak çözümleniyor.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "<PERSON><PERSON><PERSON>, derleyicinin 'new.target' meta-özellik başvurusu yakalamak için kullandığı '_newTarget' değişken bildirimi olarak çözümleniyor.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "<PERSON><PERSON><PERSON>, derleyicinin 'this' ba<PERSON><PERSON><PERSON>unu yakalamak iç<PERSON> kull<PERSON> '_this' değişken bildirimi olarak çözümleniyor.", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "İfade türü --isolatedDeclarations ile çıkarsanamaz.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "Extends yan tümcesi --isolatedDeclarations içeren bir ifade içeremez.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "Çıkarsanan '{0}' tü<PERSON><PERSON> i<PERSON>in Extends yan tümcesi '{1}' özel adı içeriyor veya kullanıyor.", "Extract_base_class_to_variable_90064": "Temel sınıfı değişken olarak ayıkla", "Extract_binding_expressions_to_variable_90066": "Bağlayıcı ifadeleri değişkene ayıkla", "Extract_constant_95006": "Sabiti ayıkla", "Extract_default_export_to_variable_90065": "Varsayılan dışarı aktarmayı değişken olarak ayıkla", "Extract_function_95005": "İşlevi ayıkla", "Extract_to_0_in_1_95004": "{1} içindeki {0} konumuna ayıkla", "Extract_to_0_in_1_scope_95008": "{1} kapsamındaki {0} konumuna ayıkla", "Extract_to_0_in_enclosing_scope_95007": "Çevreleyen kapsamdaki {0} konumuna ayıkla", "Extract_to_interface_95090": "<PERSON><PERSON><PERSON>", "Extract_to_type_alias_95078": "<PERSON><PERSON><PERSON> adına a<PERSON>", "Extract_to_typedef_95079": "typedef'e ayıkla", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "Değişken olarak ayıkla ve 'typeof {0} olarak {0}' il<PERSON>", "Extract_type_95077": "Türü ayıkla", "FILE_6035": "DOSYA", "FILE_OR_DIRECTORY_6040": "DOSYA VEYA DİZİN", "Failed_to_find_peerDependency_0_6283": "peerDependency '{0}' bulunamadı.", "Failed_to_resolve_under_condition_0_6415": "'{0}' koşulu altında çözümlenemedi.", "Fallthrough_case_in_switch_7029": "switch deyi<PERSON>e sonraki if<PERSON>ye geçiş.", "File_0_does_not_exist_6096": "'{0}' ad<PERSON>ı dosya yok.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "'{0}' <PERSON><PERSON><PERSON>, önceden önbelleğe alınan aramalara göre mevcut değil.", "File_0_exists_according_to_earlier_cached_lookups_6239": "'{0}' <PERSON><PERSON><PERSON>, önceden önbelleğe alınan aramalara göre mevcut.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "'{0}' ad<PERSON><PERSON> dosya var; bunu bir çözüm<PERSON>e sonucu olarak kullanın.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "'{0}' dosyası desteklenmeyen uzantıya sahip. Yalnızca şu uzantılar desteklenir: {1}.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "'{0}' dosyası bir JavaScript dosyasıdır. 'allowJs' se<PERSON><PERSON><PERSON><PERSON> mi etkinleştirmek istediniz?", "File_0_is_not_a_module_2306": "'{0}' dos<PERSON><PERSON> bir mod<PERSON><PERSON>.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "'{0}' dosyas<PERSON>, '{1}' projesinin dosya listesinde değil. Projelerin tüm dosyaları listelemesi veya bir 'include' deseni kullanması gerekir.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "'{0}' dosyası, 'rootDir' '{1}' dizin<PERSON><PERSON>. 'rootDir' dizininin tüm kaynak dosyalarını içermesi bekleniyor.", "File_0_not_found_6053": "'{0}' dos<PERSON><PERSON> bulunamadı.", "File_Management_6245": "<PERSON><PERSON><PERSON>", "File_appears_to_be_binary_1490": "Do<PERSON>a ikili gibi görünüyor.", "File_change_detected_Starting_incremental_compilation_6032": "Do<PERSON>a <PERSON>şikliği algılandı. Artımlı derleme başlatılıyor...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "'{0}', \"type\" alanına sahip olmadığından dosya CommonJS modülüdür", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "'{0}', de<PERSON><PERSON> \"module\" olma<PERSON> \"type\" alanına sahip <PERSON>uğundan dosya CommonJS modülüdür", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "'package.json' bulunamadığından dosya CommonJS modülüdür", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "'{0}', de<PERSON><PERSON> \"module\" olan \"type\" alanına sahip <PERSON>ğundan dosya ECMAScript modülüdür", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Bir CommonJS modülü olan dosya bir ES modülüne dönüştürülebilir.", "File_is_default_library_for_target_specified_here_1426": "<PERSON><PERSON><PERSON>, beli<PERSON><PERSON><PERSON> hede<PERSON> i<PERSON><PERSON> kitaplıktır.", "File_is_entry_point_of_type_library_specified_here_1419": "<PERSON><PERSON><PERSON>, beli<PERSON>ilen tür kitaplığının giriş noktasıdır.", "File_is_included_via_import_here_1399": "<PERSON><PERSON><PERSON>ya, içeri aktarma aracılığıyla eklenmiştir.", "File_is_included_via_library_reference_here_1406": "<PERSON><PERSON><PERSON>ya, kitaplık başvurusu aracılığıyla eklenmiştir.", "File_is_included_via_reference_here_1401": "<PERSON><PERSON><PERSON>, başvuru aracılığıyla eklenmiştir.", "File_is_included_via_type_library_reference_here_1404": "<PERSON><PERSON><PERSON>, tür <PERSON>lığı başvurusu aracılığıyla eklenmiştir.", "File_is_library_specified_here_1423": "<PERSON><PERSON><PERSON>, beli<PERSON><PERSON>n kitaplıktır.", "File_is_matched_by_files_list_specified_here_1410": "<PERSON><PERSON><PERSON>, beli<PERSON>ilen 'dosyalar' list<PERSON>ne göre eşleştirilir.", "File_is_matched_by_include_pattern_specified_here_1408": "<PERSON><PERSON><PERSON>, beli<PERSON><PERSON>n ekleme desenine göre eşleştirilir.", "File_is_output_from_referenced_project_specified_here_1413": "<PERSON><PERSON><PERSON>, belirtilmiş başvurulan projenin ç<PERSON>ışıdır.", "File_is_output_of_project_reference_source_0_1428": "<PERSON><PERSON><PERSON>, '{0}' proje ba<PERSON><PERSON><PERSON> ka<PERSON>ğının <PERSON>", "File_is_source_from_referenced_project_specified_here_1416": "<PERSON><PERSON><PERSON>, belirtilmiş başvurulan projenin ka<PERSON>ğıdır.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "'{0}' dosya adı<PERSON>, zaten eklenmiş olan '{1}' dosya adından tek farkı, büyük/küçük harf kullanımı.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "<PERSON><PERSON><PERSON> adı '{0}', '{1}' uzant<PERSON><PERSON><PERSON>na sahip - bunun yerine '{2}' aranıyor.", "File_name_0_has_a_1_extension_stripping_it_6132": "'{0}' dosya adında '{1}' uzantısı var; uzantı ayrılıyor.", "File_redirects_to_file_0_1429": "<PERSON><PERSON><PERSON>, '{0}' <PERSON><PERSON><PERSON><PERSON> ye<PERSON>ö<PERSON>i<PERSON>r", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> dizin joker karakterinden ('**') sonra görünen bir üst dizin ('..') içeremez: '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> dizin joker karakter ('**') ile bitemez: '{0}'.", "Filters_results_from_the_include_option_6627": "`include` seçeneğinden sonuçları filtreler.", "Fix_all_detected_spelling_errors_95026": "Algılanan tüm yazım hatalarını düzelt", "Fix_all_expressions_possibly_missing_await_95085": "'await' de<PERSON><PERSON><PERSON> eksik olabileceği tüm ifadeleri dü<PERSON>", "Fix_all_implicit_this_errors_95107": "<PERSON><PERSON>m örtük 'this' hatalarını onar", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Asenkron işlevlerin tüm hatalı dönüş türlerini onar", "Fix_all_with_type_only_imports_95182": "Tümünü yalnızca tür içeri aktarmaları ile düzelt", "Found_0_errors_6217": "{0} hata bulundu.", "Found_0_errors_Watching_for_file_changes_6194": "{0} hata bulund<PERSON>. <PERSON><PERSON><PERSON>iklikleri izleniyor.", "Found_0_errors_in_1_files_6261": "{1} dos<PERSON><PERSON> {0} hata bulundu.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Aynı dosyada {0} hata bulundu, ba<PERSON>langıç: {1}", "Found_1_error_6216": "1 hata bulundu.", "Found_1_error_Watching_for_file_changes_6193": "1 hata bulundu. <PERSON><PERSON><PERSON>şiklikleri izleniyor.", "Found_1_error_in_0_6259": "Şurada 1 hata bulundu: {0}", "Found_package_json_at_0_6099": "'{0}' içinde 'package.json' bulundu.", "Found_peerDependency_0_with_1_version_6282": "'{1}' s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sahip peerDependency '{0}' bulundu.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "Katı modda 'ES5' hedeflenirken blokların içinde işlev bildirimlerine izin verilmiyor.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "Katı modda 'ES5' hedeflenirken blokların içinde işlev bildirimlerine izin verilmiyor. Sınıf tanımları otomatik olarak katı moddadır.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "Katı modda 'ES5' hedeflenirken blokların içinde işlev bildirimlerine izin verilmiyor. Modüller otomatik olarak katı moddadır.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Dönüş türü ek açıklaması bulunmayan işlev ifadesi, örtük olarak '{0}' dönüş türüne sahip.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "<PERSON><PERSON>lev uygulaması yok veya bildirimden hemen sonra gelmiyor.", "Function_implementation_name_must_be_0_2389": "<PERSON><PERSON>lev uygulamasının adı '{0}' olmalıdır.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Dönüş türü ek açıklamasına sahip olmadığından ve doğrudan veya dolaylı olarak dönüş ifadelerinden birinde kendine başvurulduğ<PERSON>n i<PERSON>lev, örtük olarak 'any' tür<PERSON>ne sa<PERSON>r.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "İşlevin sonunda return deyimi eksik ve dönüş türü 'undefined' içermiyor.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "İşlevin --isolatedDeclarations ile açık bir dönüş türü ek açıklamasına sahip olması gerekir.", "Function_not_implemented_95159": "<PERSON><PERSON><PERSON> uygulanmadı.", "Function_overload_must_be_static_2387": "<PERSON>şlev aşırı yüklemesi statik olmalıdır.", "Function_overload_must_not_be_static_2388": "<PERSON><PERSON>lev aşırı yüklemesi statik olmamalıdır.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "İşlev türü gösterimi bir birleşim türünde kullanıldığında parantez içine alınmalıdır.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "İşlev türü gösterimi bir kesişim türünde kullanıldığında parantez içine alınmalıdır.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Dönüş türü ek açıklaması bulunmayan işlev türü, örtük olarak '{0}' dönüş türüne sahip.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "Gövdelere sahip işlev yalnızca çevresel sınıflarla birleştirebilir.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Projenizdeki TypeScript ve JavaScript dosyalarından .d.ts dosyaları oluşturun.", "Generate_get_and_set_accessors_95046": "'get' ve 'set' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oluştur<PERSON>", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Tüm geçersiz kılma özellikleri için 'get' ve 'set' erişimcileri oluşturun", "Generates_a_CPU_profile_6223": "Bir CPU profili oluşturur.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Karş<PERSON><PERSON><PERSON>k gelen her '.d.ts' dosyası için bir kaynak eşlemesi oluşturur.", "Generates_an_event_trace_and_a_list_of_types_6237": "<PERSON><PERSON> ve tür listesi oluşturur.", "Generates_corresponding_d_ts_file_6002": "İlgili '.d.ts' dosyasını oluşturur.", "Generates_corresponding_map_file_6043": "İlgili '.map' dosyasını oluşturur.", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "Oluşturucu örtük olarak '{0}' bekletme türüne sahip. Bir dönüş türü ek açıklaması sağlamayı deneyin.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Çevresel bağlamda oluşturuculara izin verilmez.", "Generic_type_0_requires_1_type_argument_s_2314": "'{0}' gene<PERSON> tü<PERSON>, {1} tür ba<PERSON><PERSON><PERSON><PERSON><PERSON>ğişkenini gerektiriyor.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "'{0}' genel tü<PERSON> {1} ile {2} a<PERSON><PERSON><PERSON> bağımsız değişken gerektirir.", "Global_module_exports_may_only_appear_at_top_level_1316": "Genel modül dışarı aktarmaları yalnızca en üst düzeyde görünebilir.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Genel modül dışarı aktarmaları yalnızca bildirim dosyalarında görünebilir.", "Global_module_exports_may_only_appear_in_module_files_1314": "Genel modül dışarı aktarmaları yalnızca modül dosyalarında görünebilir.", "Global_type_0_must_be_a_class_or_interface_type_2316": "'{0}' genel tü<PERSON><PERSON>, bir sınıf veya arabirim türü olmalıdır.", "Global_type_0_must_have_1_type_parameter_s_2317": "'{0}' gene<PERSON> tü<PERSON>, {1} türünde parametre içermelidir.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "'--incremental' ve '--watch' içinde yeniden derlemelerin olması, bir dosya içindeki değişikliklerin yalnızca doğrudan buna bağımlı olan dosyaları etkileyeceğini varsayar.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "'incremental' ve 'watch' modu kullanan proje<PERSON>deki ye<PERSON>den der<PERSON>, bir dosyada yapılan değişikliklerin yalnızca bu dosyaya bağımlı olan dosyaları etkileyeceğinin varsayılmasını sağla.", "Hexadecimal_digit_expected_1125": "Onaltılık basamak bekleniyor.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Tanımlayıcı bekleniyor. '{0}' modülün en üst düzeyinde ayrılmış bir sözcüktür.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Tan<PERSON>mlayıcı bekleniyor. '{0}', katı modda ayrılmış bir sözcüktür.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Tanımlayıcı bekleniyor. '{0}', katı modda ayrılmış bir sözcüktür. Sınıf tanımları otomatik olarak katı moddadır.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Tanımlayıcı bekleniyor. '{0}', katı modda ayrılmış bir sözcüktür. Modüller otomatik olarak katı moddadır.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Tanımlayıcı bekleniyor. '{0}', burada kullanılamayan ayrılmış bir sözcüktür.", "Identifier_expected_1003": "Tanımlayıcı bekleniyor.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Tanımlayıcı bekleniyor. '__esModule', ECMAScript modülleri dönüştürülürken, dışarı aktarılan bir işaretçi olarak ayrılmış.", "Identifier_or_string_literal_expected_1478": "Tanımlayıcı veya sabit değerli dize bekleniyor.", "Identifier_string_literal_or_number_literal_expected_1496": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sabit değ<PERSON>li dize veya sabit değerli sayı bekleniyor.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "'{0}' paketi bu mod<PERSON><PERSON>ü gerçekten kullanıma sunarsa, 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}' öğesini düzeltmek için bir çekme isteği göndermeyi deneyin", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "‘{0}’ paketi bu modülü fiili olarak kullanıma sunuy<PERSON>a `'{1}' mod<PERSON><PERSON><PERSON><PERSON><PERSON> bildir;` ifadesini içeren yeni bir bildirim (.d.ts) dosyası eklemeyi deneyin", "Ignore_this_error_message_90019": "Bu hata iletisini yoksay", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "tsconfig.json yok say<PERSON>, beli<PERSON>ilen dosyaları varsayılan derleyici seçenekleriyle derler.", "Implement_all_inherited_abstract_classes_95040": "Devralınan tüm soyut sınıfları uygula", "Implement_all_unimplemented_interfaces_95032": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tüm arabirimleri uygula", "Implement_inherited_abstract_class_90007": "Devralınan soyut sınıfı uygula", "Implement_interface_0_90006": "'{0}' <PERSON><PERSON><PERSON><PERSON>", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "'{1}' özel adına sahip veya bu adı kullanan '{0}' dışarı aktarılan sınıfının yan tümcesini uygular.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "'symbol' öğesinin örtük olarak 'string' tür<PERSON><PERSON> dönüştürülmesi işlemi çalışma zamanında başarısız olur. Bu ifadeyi 'String(...)' içinde sarmalamayı düşünün.", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "'{0}' içeri aktarması bu dosyada kullanılan genel değerle çakışıyor, bu nedenle 'isolatedModules' etkinken yalnızca tür içeri aktarması ile bildirilmelidir.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "'{0}' içeri aktarması yerel değerle çakışıyor, bu nedenle 'isolatedModules' etkinken yalnızca tür içeri aktarması ile bildirilmelidir.", "Import_0_from_1_90013": "'{0}' <PERSON><PERSON><PERSON><PERSON> \"{1}\" kaynağından içeri aktar", "Import_assertion_values_must_be_string_literal_expressions_2837": "İçeri aktarma onaylama <PERSON>i, dize sabit ifadeleri olmalıdır.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "CommonJS 'require' çağr<PERSON>larına derlenen deyimler için içeri aktarma onaylamalarına izin verilmiyor.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "İçeri aktarma onayları, yalnızca '--module' seçeneği 'esnext', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında desteklenir.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "İçeri aktarma onayları, yalnızca tür içeri aktarmaları veya dışarı aktarmaları ile kullanılamaz.", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "İçeri aktarma onaylamaları içeri aktarma öznitelikleriyle değiştirildi. 'assert' yerine 'with' k<PERSON><PERSON><PERSON><PERSON>.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "ECMAScript modülleri hedeflenirken içeri aktarma ataması kullanılamaz. Bunun yerine 'import * as ns from \"mod\"', 'import {a} from \"mod\"', 'import d from \"mod\"' veya başka bir modül biçimi kullanmayı deneyin.", "Import_attribute_values_must_be_string_literal_expressions_2858": "İçeri aktarma öznitelik değerleri, sabit de<PERSON><PERSON><PERSON> dize ifadeleri olmalıdır.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "CommonJS 'require' çağr<PERSON>larına derlenen deyimler üzerinde içeri aktarma özniteliklerine izin verilmiyor.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "İçeri aktarma öznitelikleri, yalnızca '--module' seçeneği 'esnext', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında desteklenir.", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "İçeri aktarma öznitelikleri, yalnızca tür içeri aktarmaları veya dışarı aktarmaları ile kullanılamaz.", "Import_declaration_0_is_using_private_name_1_4000": "'{0}' <PERSON><PERSON><PERSON> a<PERSON> bild<PERSON>, '{1}' özel adına sahip veya bu adı kullanıyor.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "İçeri aktarma bildirimi, yerel '{0}' bild<PERSON><PERSON><PERSON>.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Ad alanındaki içeri aktarma bildirimleri bir modüle başvuramaz.", "Import_emit_helpers_from_tslib_6139": "'Tslib'den yayma yardımcılarını içeri aktar.", "Import_may_be_converted_to_a_default_import_80003": "İçeri aktarma varsayılan bir içeri aktarmaya dönüştürülebilir.", "Import_name_cannot_be_0_2438": "İçeri aktarma adı '{0}' olamaz.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Çevresel modül bildirimindeki içeri veya dışarı aktarma bildirimi, göreli modül adı aracılığıyla modüle başvuramaz.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "'{0}' içeri aktarma tanımlayıcısı, '{1}' yolundaki package.json kapsamında yok.", "Imported_via_0_from_file_1_1393": "'{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "compilerOptions içinde belirtildiği gibi 'importHelpers' öğesini içeri aktarmak için '{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "'jsx' ve 'jsxs' fabrika işlevlerini içeri aktarmak için '{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Imported_via_0_from_file_1_with_packageId_2_1394": "'{2}' paket kim<PERSON> sa<PERSON> '{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "compilerOptions içinde belirtildiği gibi 'importHelpers' öğesini içeri aktarmak için '{2}' paket kimliğine sahip '{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "'jsx' ve 'jsxs' fabrika işlevlerini içeri aktarmak için '{2}' paket kimliğine sahip '{1}' dosyasından {0} aracılığıyla içeri aktarıldı", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "Bir JSON dosyasının ECMAScript modülüne aktarılması, 'module' değeri '{0}' olarak ayarlandığında 'type: \"json\"' içeri aktarma özniteliği gerektirir.", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Modül genişletmelerinde içeri aktarmalara izin verilmez. Bunları, kapsayan dış modüle taşımanız önerilir.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Çevresel sabit listesi bildiri<PERSON>e, üye başlatıcısı sabit ifade olmalıdır.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "Birden fazla bildirime sahip sabit listesinde yalnızca bir bildirim ilk sabit listesi öğesine ait başlatıcıyı atlayabilir.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Dosyaların listesini ekleyin. Bu, `include` seçeneğinden farklı olarak glob desenlerini desteklemez.", "Include_modules_imported_with_json_extension_6197": "'.json' uzantısıyla içeri aktarılan modülleri dahil et", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Yayılan JavaScript içindeki kaynak eşlemelerine kaynak kodunu ekleyin.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Yayılan JavaScript içine kaynak eşleme dosyalarını ekleyin.", "Includes_imports_of_types_referenced_by_0_90054": "'{0}' ta<PERSON><PERSON><PERSON><PERSON>n başvurulan türlerin içeri aktarmalarını içerir", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "--watch da<PERSON> o<PERSON> üzere -w, <PERSON><PERSON> değişiklikleri için geçerli projeyi izlemeye başlayacak. Bir kez ayarlandıktan sonra, izleme modunu şununla yapılandırabilirsiniz:", "Incomplete_quantifier_Digit_expected_1505": "Eksik niceleyici. <PERSON><PERSON> bekleniyordu.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "'{0}' türünde<PERSON> dizin imzası '{1}' türünde yok.", "Index_signature_in_type_0_only_permits_reading_2542": "'{0}' türündeki dizin imzası yalnızca okumaya izin veriyor.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "'{0}' birleştirilmiş bildirimindeki bildirimlerin tümü dışarı aktarılmış veya yerel olmalıdır.", "Infer_all_types_from_usage_95023": "<PERSON><PERSON><PERSON> tü<PERSON> k<PERSON>", "Infer_function_return_type_95148": "<PERSON>şlev dönüş türünü çıkarsa", "Infer_parameter_types_from_usage_95012": "Parametre türleri için k<PERSON> çı<PERSON>ım yap", "Infer_this_type_of_0_from_usage_95080": "Kullanımdan '{0}' ö<PERSON><PERSON><PERSON> 'this' türü<PERSON><PERSON>", "Infer_type_of_0_from_usage_95011": "'{0}' tü<PERSON><PERSON> i<PERSON><PERSON> k<PERSON> çı<PERSON>ım yap", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "<PERSON><PERSON><PERSON><PERSON>f ifadelerinden çıkarım, --isolatedDeclarations ile desteklenmiyor.", "Initialize_property_0_in_the_constructor_90020": "Oluşturucu içinde '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>lat", "Initialize_static_property_0_90021": "'{0}' stat<PERSON> özelliğini ba<PERSON>lat", "Initializer_for_property_0_2811": "'{0}' özelliği iç<PERSON> ba<PERSON>ı", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "'{0}' örnek üyesi değişkeninin başlatıcısı, oluşturucuda bildirilen '{1}' tanımlayıcısına başvuramaz.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Çevresel bağlamlarda başlatıcılara izin verilmez.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Bir TypeScript projesi başlatır ve bir tsconfig.json dosyası oluşturur.", "Inline_variable_95184": "Satır içi değ<PERSON>şken", "Insert_command_line_options_and_files_from_a_file_6030": "Dosyadaki komut satırı seçeneklerini ve dosyaları ekleyin.", "Install_0_95014": "'{0}' yükle", "Install_all_missing_types_packages_95033": "<PERSON>ü<PERSON> eksik tür pake<PERSON><PERSON> yükle", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "'{0}' a<PERSON><PERSON><PERSON>, aynı anda '{1}' ve '{2}' t<PERSON><PERSON><PERSON> genişletemez.", "Interface_0_incorrectly_extends_interface_1_2430": "'{0}' a<PERSON><PERSON><PERSON>, '{1}' a<PERSON><PERSON><PERSON> yanlış genişletiyor.", "Interface_declaration_cannot_have_implements_clause_1176": "<PERSON><PERSON><PERSON>, 'implements' yan tümcesine sahip olamaz.", "Interface_must_be_given_a_name_1438": "Arabirime bir ad verilmesi gerekir.", "Interface_name_cannot_be_0_2427": "<PERSON><PERSON><PERSON> adı '{0}' olamaz.", "Interop_Constraints_6252": "Birlikte Çalışma Kısıtlamaları", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "'undefined' eklemek yerine isteğe bağlı özellik türlerini yazıldıkları gibi yorumlayın.", "Invalid_character_1127": "Geçersiz karakter.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "Geçersiz '{0}' içeri aktarma tanımlayıcısında olası çözünürlük yok.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Genişletmedeki modül adı geçersiz. '{0}' mod<PERSON><PERSON><PERSON>, '{1}' konumundaki türü belirsiz ve genişletilemeyen bir modüle çözümleniyor.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Genişletmedeki modül adı geçersiz; '{0}' adlı modül bulunamıyor.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Yeni ifadeden geçersiz isteğe bağlı zincir. '{0}()' çağrısı mı yapmak istediniz?", "Invalid_reference_directive_syntax_1084": "Geçersiz 'reference' yönergesi söz dizimi.", "Invalid_syntax_in_decorator_1498": "Dekoratörde geçersiz söz dizimi.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Geçersiz '{0}' kullanımı. Bu ifade bir sınıf statik bloğu içinde kullanılamaz.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Geçersiz '{0}' kullanımı. Modüller otomatik olarak katı moddadır.", "Invalid_use_of_0_in_strict_mode_1100": "Katı modda geçersiz '{0}' kullanımı.", "Invalid_value_for_ignoreDeprecations_5103": "'--ignoreDeprecations' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "'jsxFactory' de<PERSON><PERSON> geçersiz. '{0}' geçerli bir tanımlayıcı veya tam ad değil.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "'jsxFragmentFactory' değeri geçersiz. '{0}' geçerli bir tanımlayıcı veya tam ad değil.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "'--reactNamespace' iç<PERSON> ge<PERSON>. '{0}' geçerli bir tanımlayıcı değil.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Bu iki şablon ifadesini ayırmak için virgül koymamış olabilirsiniz. <PERSON><PERSON> <PERSON><PERSON><PERSON>, çağrılamayacak etiketli bir şablon ifadesi oluşturur.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "'{0}' öğ<PERSON> türü geçerli bir JSX öğesi değil.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "'{0}' örnek türü geçerli bir JSX öğesi değil.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "'{0}' dönüş türü geçerli bir JSX öğesi değil.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "'{0}' tür<PERSON> geçerli bir JSX öğe türü de<PERSON>.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSDoc '@{0} {1}', 'extends {2}' yan tü<PERSON><PERSON>.", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc '@{0}' bir sınıfa eklenmemiş.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' yalnızca bir imzanın son parametresi içinde görünebilir.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc '@param' etiketinin adı '{0}' ancak bu ada sahip bir parametre yok.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc '@param' etiketi '{0}' adına sahip ancak bu ada sahip bir parametre yok. Bir dizi türü olsaydı 'arguments' ile eşleşirdi.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "JSDoc typedef, TypeScript türüne dönüştürülmüş olabilir.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc '@typedef' etiketi bir tür ek açıklamasına sahip olmalıdır veya sonrasında '@property' ya da '@member' etiketlerinden biri gelmelidir.", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "JSDoc typedef’leri, TypeScript türle<PERSON> dö<PERSON>ştür<PERSON>lmüş olabilir.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc türleri yalnızca belge açıklamalarının içinde kullanılabilir.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc türleri TypeScript türlerine taşınabilir.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "JSX özniteliklerine yalnızca boş olmayan 'expression' ifadesi atanabilir.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "'{0}' adlı JSX öğesine karşılık gelen bir kapatma etiketi yok.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "JSX öğe sınıfı, '{0}' özelliğine sahip olmadığı için öznitelikleri desteklemiyor.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "Herhangi bir arabirim 'JSX.{0}' öğ<PERSON> olmadığı için JSX öğesi örtük olarak 'any' tür<PERSON>ne sahip.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "Genel türdeki 'JSX.Element' öğesi olmadığı için JSX öğesi örtük olarak 'any' türüne sahip.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX öğesi türü '{0}', oluşturma veya çağrı imzasına sahip değil.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX öğeleri aynı ada sahip birden fazla özniteliğe sahip olamaz.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX ifadeleri virgül işlecini kullanamaz. Bir dizi mi yazmak istediniz?", "JSX_expressions_must_have_one_parent_element_2657": "JSX ifadelerinin bir üst öğesi olmalıdır.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX parçasına karşılık gelen bir kapatma etiketi yok.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "JSX özellik erişimi ifadeleri JSX ad alanı adlarını içeremez", "JSX_spread_child_must_be_an_array_type_2609": "JSX yayılma alt öğesi, bir dizi türü olmalıdır.", "JavaScript_Support_6247": "JavaScript Desteği", "Jump_target_cannot_cross_function_boundary_1107": "Atlama he<PERSON><PERSON> i<PERSON> sınırını geçemez.", "KIND_6034": "TÜR", "Keywords_cannot_contain_escape_characters_1260": "<PERSON><PERSON><PERSON> s<PERSON>ükler kaçış karakterleri içeremez.", "LOCATION_6037": "KONUM", "Language_and_Environment_6254": "<PERSON><PERSON> ve <PERSON>", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Virgül işlecinin sol tarafı kullanılmıyor ve herhangi bir yan etkisi yok.", "Library_0_specified_in_compilerOptions_1422": "compilerOptions içinde belirtilen '{0}' kitaplığı", "Library_referenced_via_0_from_file_1_1405": "'{1}' dos<PERSON>ından '{0}' aracılığıyla başvurulan kitaplık", "Line_break_not_permitted_here_1142": "Burada satır sonuna izin verilmez.", "Line_terminator_not_permitted_before_arrow_1200": "Oktan önce satır sonlandırıcısına izin verilmez.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Bir modül çözümlenirken aranacak dosya adı son e<PERSON><PERSON><PERSON> listesi.", "List_of_folders_to_include_type_definitions_from_6161": "Eklenecek tür tanımlarının alınacağı klasörlerin listesi.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Birleştirilmiş <PERSON>, çalışma zamanında proje yapısını temsil eden kök klasörlerin listesi.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "'{1}' kök dizin<PERSON> '{0}' y<PERSON><PERSON><PERSON><PERSON><PERSON>; aday konumu: '{2}'.", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "'node_modules' klasöründen '{0}' m<PERSON><PERSON><PERSON><PERSON>, hede<PERSON> <PERSON> tü<PERSON>: {1}.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "<PERSON><PERSON><PERSON><PERSON>, dosya/klas<PERSON>r o<PERSON>ak y<PERSON>; aday modül konumu '{0}'; hedef dosya tü<PERSON>: {1}.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "<PERSON><PERSON>, <language> veya <language>-<territory> biçiminde olmalıdır. Örneğin, '{0}' veya '{1}'.", "Log_paths_used_during_the_moduleResolution_process_6706": "'moduleResolution' i<PERSON><PERSON>i sı<PERSON>ında kullanılan yolları günlüğe kaydet.", "Longest_matching_prefix_for_0_is_1_6108": "'{0}' i<PERSON><PERSON> en uzun ön ek: '{1}'.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "'node_modules' klasöründe aranıyor; ilk konum: '{0}'.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Tüm 'super()' çağrılarını kendi oluşturucularının ilk deyimi yap", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "<PERSON><PERSON>, sayı veya simge yerine yalnızca dönüş dizelerinin anahtarını oluşturun. Eski seçenek.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Oluşturucudaki ilk deyime 'super()' tarafından çağrı yapılmasını sağla", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Eşleştirilmiş nesne türü örtük olarak 'any' şablon türüne sahip.", "Mark_array_literal_as_const_90070": "Sabit değerli dizileri const olarak işaretle", "Matched_0_condition_1_6403": "'{0}' koş<PERSON> '{1}' <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Varsayılan '**/*' ekleme des<PERSON>", "Matched_by_include_pattern_0_in_1_1407": "'{1}' i<PERSON><PERSON><PERSON><PERSON> '{0}' ekleme desenine göre e<PERSON>", "Member_0_implicitly_has_an_1_type_7008": "'{0}' <PERSON><PERSON><PERSON> ö<PERSON> o<PERSON> '{1}' tü<PERSON><PERSON><PERSON> sa<PERSON>.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "'{0}' üyesi örtük olarak bir '{1}' tür<PERSON>ne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "Merge_conflict_marker_encountered_1185": "Birleştirme çakışması işaretçisiyle karşılaşıldı.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "'{0}' bi<PERSON><PERSON><PERSON><PERSON><PERSON> bildirimi, var<PERSON><PERSON><PERSON> bir dışarı aktarma bildirimini içeremez. Bunun yerine ayrı bir 'export default {0}' bildirimi eklemeyi göz önünde bulundurun.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "'{0}' meta-özelliğine yalnızca bir işlev bildiriminin, işlev ifadesinin veya oluşturucunun gövdesinde izin verilir.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "'{0}' metodu abstract olarak işaretlendiğinden bir uygulamaya sahip olamaz.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Dışarı aktarılan arabirimin '{0}' metodu, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Dışarı aktarılan arabirimin '{0}' metodu, '{1}' özel adına sahip veya bu adı kullanıyor.", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "Metodun --isolatedDeclarations ile açık bir dönüş türü ek açıklamasına sahip olması gerekir.", "Method_not_implemented_95158": "Metot uygulanmadı.", "Modifiers_cannot_appear_here_1184": "Değiştiriciler burada görüntülenemez.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "'{0}' m<PERSON><PERSON><PERSON><PERSON> yalnızca varsayılan olarak '{1}' bayrağı kullanılarak içeri aktarılabilir", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "'{0}' m<PERSON><PERSON><PERSON><PERSON> bu yapı kullanılarak içe aktarılamaz. Belirtici yalnızca 'require' ile içe aktarılamayan bir ES modülüne çözümlenir. Bunun yerine bir ECMAScript içe aktarma kullanın.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "'{0}' mod<PERSON><PERSON><PERSON> '{1}' öğesini yerel olarak bildiriyor ancak '{2}' olarak dışarı aktarıldı.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "'{0}' modül<PERSON> '{1}' öğesini yerel olarak bildiriyor ancak dışarı aktarılmadı.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "'{0}' m<PERSON><PERSON><PERSON><PERSON> bir türe başvurmuyor ancak burada bir tür olarak kullanılmış. Şunu mu demek istediniz?: 'typeof import('{0}')'", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "'{0}' m<PERSON><PERSON><PERSON><PERSON> bir de<PERSON>ere başvurmuyor ancak burada bir değer olarak kullanılmış.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "{0} m<PERSON><PERSON><PERSON><PERSON>, '{1}' adlı bir üyeyi zaten dışarı aktardı. Belirsizliği çözmek için açık olarak yeniden dışarı aktarmayı göz önünde bulundurun.", "Module_0_has_no_default_export_1192": "'{0}' m<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> var<PERSON>lan dışarı aktarma yok.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "'{0}' m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> varsayılan dışarı aktarması yok. Bunun yerine 'import { {1} } from {0}' kullanmak mı istediniz?", "Module_0_has_no_exported_member_1_2305": "'{0}' m<PERSON><PERSON><PERSON><PERSON>, dışarı aktarılan '{1}' ü<PERSON>ine sahip değil.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "'{0}' m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n dışarı aktarılmış '{1}' üyesi yok. Bunun yerine 'import {1} from {0}' kullanmak mı istediniz?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "'{0}' m<PERSON><PERSON><PERSON><PERSON>, aynı ada sahip bir yerel bildirim tarafından gizleniyor.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "'{0}' modülü 'export =' kullanıyor ve 'export *' ile birlikte kullanılamaz.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "'{0}' m<PERSON><PERSON><PERSON><PERSON>, '{1}' dos<PERSON><PERSON><PERSON> yerel olarak bildirilmiş çevresel modül olarak çözümlendi.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "'{0}' mod<PERSON><PERSON><PERSON> '{1}' o<PERSON><PERSON>, ancak '--allowArbitraryExtensions' ayarlanmadı.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "'{0}' mod<PERSON><PERSON><PERSON> '{1}' olar<PERSON> çözüldü ancak '--jsx' ayarlanmadı.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "'{0}' mod<PERSON><PERSON><PERSON> '{1}' olarak çözümlendi ancak '--resolveJsonModule' kullanılmadı.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Modül bildirim adları yalnızca ' veya \" tırnak içine alınmış dizeleri kullanabilir.", "Module_name_0_matched_pattern_1_6092": "<PERSON><PERSON><PERSON><PERSON> adı: '{0}', e<PERSON><PERSON><PERSON><PERSON> desen: '{1}'.", "Module_name_0_was_not_resolved_6090": "======== '{0}' modül adı çözümlenemedi. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== '{0}' modül adı '{1}' öğesine başarıyla çözümlendi. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== '{0}' modül adı '{2}' Paket <PERSON> '{1}' olarak başarıyla çözümlendi. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "<PERSON><PERSON><PERSON><PERSON>özümleme tü<PERSON>ü be<PERSON>, '{0}' kullanılıyor.", "Module_resolution_using_rootDirs_has_failed_6111": "'rootDirs' k<PERSON><PERSON><PERSON> modül çözümleme başarısız oldu.", "Modules_6244": "<PERSON><PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Etiketlenmiş demet öğesi <PERSON>ğiştiricilerini etiketlere taşı", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "Varsayılan dışarı aktarmadaki ifadeyi bir değişkene taşıyın ve bir tür ek açıklaması ekleyin.", "Move_to_a_new_file_95049": "<PERSON>ni bir dos<PERSON>ya ta<PERSON>ı", "Move_to_file_95178": "Dosyaya taşı", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Birbirini izleyen birden çok sayısal ayırıcıya izin verilmez.", "Multiple_constructor_implementations_are_not_allowed_2392": "Birden çok oluşturucu uygulamasına izin verilmez.", "NEWLINE_6061": "YENİ SATIR", "Name_is_not_valid_95136": "Ad geçerli değil", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "Adlandırılmış yakalama grupları yalnızca 'ES2018' veya üzeri hedeflenirken kullanılabilir.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "Aynı ada sahip adlandırılmış yakalama grupları birbirini dışlamalıdır.", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "'module', '{0}' o<PERSON>ak ayarlandığında JSON dosyasından ECMAScript modülüne adlandırılmış içeri aktarmalara izin verilmez.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "'{1}' ve '{2}' türündeki '{0}' adlı özellikler aynı değil.", "Namespace_0_has_no_exported_member_1_2694": "'{0}' ad alanında dışarı aktarılan '{1}' üyesi yok.", "Namespace_must_be_given_a_name_1437": "Ad alanına bir ad verilmesi gerekir.", "Namespace_name_cannot_be_0_2819": "Ad alanı “{0}” olamaz.", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "'{0}' etkinken genel betik dosyalarında ad alanlarına izin verilmiyor. Bu dosyanın genel bir betik olması amaçlanmamışsa, 'moduleDetection'i 'force' olarak ayarlayın veya boş bir 'export {}' deyimi ekleyin.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "'this' parametrelerine ne dekoratörler ne de değiştiriciler uygulanamaz.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Hiçbir temel oluşturucu, beli<PERSON><PERSON>n tür ba<PERSON><PERSON><PERSON><PERSON>z değişkeni sayısına sa<PERSON> değil.", "No_constituent_of_type_0_is_callable_2755": "'{0}' türünde çağrılabilir bileşen yok.", "No_constituent_of_type_0_is_constructable_2759": "'{0}' türünde oluşturulabilir bileşen yok.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "'{1}' türü ü<PERSON>inde '{0}' türünde parametreye sahip dizin imzası bulunamadı.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "'{0}' ya<PERSON><PERSON><PERSON><PERSON><PERSON> dosyasında giriş bulunamadı. Belirtilen 'include' yolları: '{1}', 'exclude' yolları: '{2}'.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Artık desteklenmiyor. <PERSON><PERSON><PERSON>, dosyaları okumak için metin kodlamasını el ile ayarlayın.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "{0} bağımsız değişken bekleyen aşırı yükleme yok ancak {1} veya {2} bağımsız değişken bekleyen aşırı yüklemeler var.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "{0} tür bağımsız değişkeni bekleyen aşırı yükleme yok ancak {1} veya {2} tür bağımsız değişkeni bekleyen aşırı yüklemeler var.", "No_overload_matches_this_call_2769": "Bu çağrıyla eşleşen aşırı yükleme yok.", "No_type_could_be_extracted_from_this_type_node_95134": "Bu tür düğümünden tür ayıklanamadı", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "'{0}' toplu özelliği için kapsamda değer yok. Bir değer tanımlayın ya da bir başlatıcı sağlayın.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "Soyut olmayan '{0}' sınıfı, '{2}' sınıfından devralınan {1} soyut üyesini uygulamaz.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "Şu '{1}' üyelerinde soyut olmayan sınıf '{0}' uygulamalar eksik: {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "Şu '{1}' üyelerinde soyut olmayan sınıf '{0}' uygulamalar eksik: {2} ve {3} tane daha.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Soyut olmayan sınıf if<PERSON>, '{1}' sınıfından devralınan '{0}' soyut üyesini uygulamıyor.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "Şu '{0}' üyelerinde soyut olmayan sınıf ifadesinin uygulamaları eksik: {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "Şu '{0}' üyelerinde soyut olmayan sınıf ifadesinde uygulamalar eksik: {1} ve {2} tane daha.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "<PERSON>ull o<PERSON>yan onaylamalar yalnızca TypeScript dosyalarında kullanılabilir.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "'baseUrl' ayarlanmadığında göreli olmayan yollara izin verilmez. Başına './' koymayı deneyin", "Non_simple_parameter_declared_here_1348": "<PERSON><PERSON><PERSON> parametre burada bildiri<PERSON>i.", "Not_all_code_paths_return_a_value_7030": "<PERSON>üm kod yolları bir değer döndürmez.", "Not_all_constituents_of_type_0_are_callable_2756": "'{0}' türündeki tüm bileşenler çağrılabilir değil.", "Not_all_constituents_of_type_0_are_constructable_2760": "'{0}' türündeki tüm bileşenler oluşturulabilir değil.", "Numbers_out_of_order_in_quantifier_1506": "Sayılar niceleyicide sıranın dışında.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "2^53 veya üzeri mutlak değerlere sahip sayısal sabit de<PERSON><PERSON>, tams<PERSON><PERSON> olarak doğru bir şekilde temsil edilemeyecek kadar büyüktür.", "Numeric_separators_are_not_allowed_here_6188": "Burada sayısal ayırıcılara izin verilmez.", "Object_is_of_type_unknown_2571": "<PERSON><PERSON>ne 'unknown' <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Object_is_possibly_null_2531": "Nesne büyük olasılıkla 'null'.", "Object_is_possibly_null_or_undefined_2533": "Nesne büyük olasılıkla 'null' veya 'undefined'.", "Object_is_possibly_undefined_2532": "Nesne büyük olasılıkla 'undefined'.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Nesne sabit değeri yalnızca bilinen özellikleri belirtebilir ve '{0}', '{1}' türünde değil.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Nesne sabit değerinde yalnızca bilinen özellikler belirtilebilir, ancak '{0}', '{1}' türünde yok. '{2}' yazmak mı istediniz?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "'{0}' nesne sabit de<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> olarak '{1}' tür<PERSON>ne sahip.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "Toplu özellikler içeren nesneler --isolatedDeclarations ile çıkarsanamaz.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "<PERSON><PERSON><PERSON> atamaları içeren nesneler --isolatedDeclarations ile çıkarsanamaz.", "Octal_digit_expected_1178": "<PERSON><PERSON><PERSON><PERSON> basamak bekleniyor.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "<PERSON><PERSON><PERSON> sınıfında sekizli kaçış dizilerine ve geri başvurulara izin verilmiyor. Bu bir kaçış dizisi olarak amaçlandıysa, yerine '{0}' kullanın.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "<PERSON><PERSON><PERSON>li kaçış dizilerine izin verilmiyor. '{0}' s<PERSON><PERSON> dizi<PERSON> kullanın.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "Sabit değ<PERSON>li sekizliklere izin verilmiyor. '{0}' s<PERSON><PERSON> di<PERSON><PERSON> kullanın.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "'{0}.{1}' değerlerinden biri '{2}' dizesidir ve diğer değerin bilinmeyen bir sayısal değer olduğu varsayılır.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "'for...in' deyiminde yalnızca tek bir değişken bildirimine izin verilir.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "'for...of' deyiminde yalnızca tek bir değişken bildirimine izin verilir.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "'new' anahtar sözcüğüyle yalnızca void işlevi çağrılabilir.", "Only_ambient_modules_can_use_quoted_names_1035": "Yalnızca çevresel modüller tırnak içinde ad kullanabilir.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "--{0} ile birlikte yalnızca 'amd' ve 'system' modülleri desteklenir.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "Yalnızca const dizileri --isolatedDeclarations ile çıkarsanabilir.", "Only_emit_d_ts_declaration_files_6014": "Yalnızca '.d.ts' bildirim dosyalarını yayımla.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "JavaScript dosyalarının değil yalnızca d.ts dosyalarının çıkışını alın.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "'super' anahtar sözcüğüyle yalnızca temel sınıfa ait ortak ve korunan metotlara erişilebilir.", "Operator_0_cannot_be_applied_to_type_1_2736": "'{0}' i<PERSON><PERSON><PERSON> '{1}' tü<PERSON><PERSON><PERSON>gu<PERSON>.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "'{0}' <PERSON><PERSON><PERSON><PERSON>, '{1}' ve '{2}' tü<PERSON><PERSON><PERSON>.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "İşleçler bir karakter sınıfı içinde karıştırılmamalıdır. Bunun yerine iç içe bir sınıfta sarmalayın.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Düzenleme sırasında bir projeyi çok projeli başvuru denetiminin dışında tutun.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "'{0}={1}' seçeneği kaldırıldı. Lütfen yapılandırmanızdan kaldırın.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "'{0}={1}' seçeneği kullanım dışı bırakıldı ve TypeScript {2} sürümünde çalışmayacak. Bu hatayı sessize almak için compilerOption '\"ignoreDeprecations\": \"{3}\"' olarak belirtin.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "'{0}' se<PERSON><PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> 'tsconfig.json' dosyasında belirtilebilir veya komut satırında 'false' veya 'null' o<PERSON><PERSON> ayarlanabilir.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "'{0}' se<PERSON><PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> 'tsconfig.json' dosyasında belirtilebilir veya komut satırında 'null' olarak ayarlanabilir.", "Option_0_can_only_be_specified_on_command_line_6266": "'{0}' seçeneği yalnızca komut satırında belirtilebilir.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "'{0} seçeneği yalnızca '--inlineSourceMap' veya '--sourceMap' seçeneği sağlandığında kullanılabilir.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "Seçenek '{0}' yalnızca 'moduleResolution' değeri 'node16', 'nodenext' veya 'bundler' o<PERSON><PERSON> ayarlandığında kullanılabilir.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "'{0}' se<PERSON><PERSON><PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON> 'module' <PERSON><PERSON><PERSON> 'preserve', 'es2015' veya üzeri olarak ayarlandığında kullanılabilir.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "'jsx' seç<PERSON>ğ<PERSON> '{1}' olduğunda '{0}' seç<PERSON><PERSON><PERSON> belirtile<PERSON>z.", "Option_0_cannot_be_specified_with_option_1_5053": "'{0}' se<PERSON><PERSON><PERSON><PERSON>, '{1}' se<PERSON><PERSON><PERSON><PERSON> ile belirtilemez.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "'{0}' se<PERSON><PERSON><PERSON><PERSON>, '{1}' se<PERSON><PERSON><PERSON><PERSON> belirtilmeden belirtilemez.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "'{1}' seç<PERSON>ği veya '{2}' seç<PERSON><PERSON>i belirtilmeden '{0}' seç<PERSON><PERSON><PERSON> belirtile<PERSON>z.", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "'{0}' seçeneği kaldırıldı. Lütfen yapılandırmanızdan kaldırın.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "'{0}' seçeneği kullanım dışı bırakıldı ve TypeScript {1} sürümünde çalışmayacak. Bu hatayı sessize almak için compilerOption '\"ignoreDeprecations\": \"{2}\"' olarak belirtin.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "'{0}' seç<PERSON>ği gereksiz ve '{1}' seç<PERSON><PERSON>i ile belirtilemez.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "'allowImportingTsExtensions' <PERSON><PERSON><PERSON><PERSON><PERSON>, yaln<PERSON>z<PERSON> 'noEmit' veya 'emitDeclarationOnly' ayarlandığında kullanılabilir.", "Option_build_must_be_the_first_command_line_argument_6369": "'--build' seçeneği ilk komut satırı bağımsız değişkeni olmalıdır.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "'--incremental' seçeneği yalnızca tsconfig kullanı<PERSON>ak, tek bir dosyada gösterilerek veya '--tsBuildInfoFile' seçeneği sağlandığında belirtilebilir.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "'isolatedModules' se<PERSON><PERSON><PERSON><PERSON>, yaln<PERSON><PERSON><PERSON> '--module' sağlandığında veya 'target' seçeneği 'ES2015' veya daha yüksek bir sürüm değerine sahip olduğunda kullanılabilir.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "'module' seçene<PERSON><PERSON> '{1}' olarak ayarlandığında 'moduleResolution' seçeneği '{0}' olarak ayarlanmalıdır (veya belirtilmemiş olarak bırakılmalıdır).", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "'moduleResolution' seçeneği '{1}' olarak ayarlandığında 'module' seçeneği '{0}' olarak ayarlanmalıdır.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "'{0}' etkinken 'preserveConstEnums' seçeneği devre dışı bırakılamaz.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "'project' <PERSON><PERSON><PERSON><PERSON><PERSON>, komut satırındaki kaynak dosyalarıyla karıştırılamaz.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "'moduleResolution' 'classic' olarak ayarlandığında '--resolveJsonModule' se<PERSON><PERSON><PERSON><PERSON> belirtilemez.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "'module' 'none', 'classic' veya 'umd' olar<PERSON> ayarlandığında '--resolveJsonModule' se<PERSON><PERSON><PERSON><PERSON> belirtilemez.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "'module' <PERSON><PERSON><PERSON> 'UMD', 'AMD' veya 'System' olarak ayarlandığında 'verbatimModuleSyntax' se<PERSON>eneği kullanılamaz.", "Options_0_and_1_cannot_be_combined_6370": "'{0}' ve '{1}' se<PERSON><PERSON><PERSON><PERSON>.", "Options_Colon_6027": "Seçenekler:", "Output_Formatting_6256": "Çıkış Biçimlendirmesi", "Output_compiler_performance_information_after_building_6615": "<PERSON><PERSON><PERSON> derleyici performans bilgilerinin çıkışını alın.", "Output_directory_for_generated_declaration_files_6166": "Oluşturulan bildirim dosyaları için çıkış dizini.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Çıkış dosyası '{0}' '{1}' kaynak dosyasından oluşturulmamış.", "Output_from_referenced_project_0_included_because_1_specified_1411": "'{1}' be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, baş<PERSON>rulan '{0}' proje<PERSON><PERSON> dahil edildi", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "'--module' 'none' olar<PERSON> be<PERSON>il<PERSON>ğ<PERSON>en, başvu<PERSON>lan '{0}' projesin<PERSON> dahil edildi", "Output_more_detailed_compiler_performance_information_after_building_6632": "<PERSON><PERSON><PERSON> derleyici performans bilgilerinin daha ayrıntılı çıkışını alın.", "Overload_0_of_1_2_gave_the_following_error_2772": "{0}/{1} aşırı yükleme '{2}' im<PERSON><PERSON>, aşağıdaki hatayı verdi.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Aşırı yükleme imzalarının hepsi soyut veya soyut olmayan olmalıdır.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Aşırı yükleme imzalarının tümü çevresel veya çevresel olmayan türde olmalıdır.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Aşırı yükleme imzalarının hepsi dışarı aktarılmış veya dışarı aktarılmamış olmalıdır.", "Overload_signatures_must_all_be_optional_or_required_2386": "Aşırı yükleme imzalarının tümü isteğe bağlı veya gerekli olmalıdır.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Aşırı yükleme imzalarının tü<PERSON>ü ortak, özel veya korumalı olmalıdır.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "'{0}' paramet<PERSON><PERSON>, kendisinden sonra bildirilen '{1}' <PERSON><PERSON><PERSON>layı<PERSON>ı<PERSON>ına başvuramaz.", "Parameter_0_cannot_reference_itself_2372": "'{0}' parametresi kendisine başvuramaz.", "Parameter_0_implicitly_has_an_1_type_7006": "'{0}' parametresi örtük olarak '{1}' tür<PERSON><PERSON> sa<PERSON>.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "'{0}' parametresi örtük olarak bir '{1}' türüne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "'{0}' parametresi, '{1}' parametresi ile aynı konumda değil.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Erişimcinin '{0}' parametresi, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor ancak adlandırılamıyor.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Erişimcinin '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Erişimcinin '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Dışarı aktarılan arabirimdeki çağrı imzasının '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Dışarı aktarılan arabirimdeki çağrı imzasının '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Dışarı aktarılan sınıftaki oluşturucunun '{0}' parametresi, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Dışarı aktarılan sınıftaki oluşturucunun '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Dışarı aktarılan sınıftaki oluşturucunun '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Dışarı aktarılan arabirimdeki oluşturucu imzasının '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Dışarı aktarılan arabirimdeki oluşturucu imzasının '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Dışarı aktarılan işlevin '{0}' parametresi, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Dışarı aktarılan işlevin '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Dışarı aktarılan işlevin '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Dışarı aktarılan arabirimin dizin imzasındaki '{0}' parametresi, '{2}' adlı özel modüldeki '{1}' adına sahip ya da bu adı kullanıyor.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Dışarı aktarılan arabirimin dizin imzasındaki '{0}' parametresi, '{1}' adına sahip ya da bu adı kullanıyor.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Dışarı aktarılan arabirimdeki metodun '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Dışarı aktarılan arabirimdeki metodun '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Dışarı aktarılan sınıftaki ortak metodun '{0}' parametresi, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Dışarı aktarılan sınıftaki ortak metodun '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Dışarı aktarılan sınıftaki ortak metodun '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Dışarı aktarılan sınıftaki ortak metodun '{0}' parametresi, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Dışarı aktarılan sınıftaki statik metodun '{0}' parametresi, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Dışarı aktarılan sınıftaki statik metodun '{0}' parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Parametre soru işareti ve başlatıcı içeremez.", "Parameter_declaration_expected_1138": "Parametre bildirimi be<PERSON>.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Parametrenin adı var ancak türü yok. Şunu mu demek istediniz: '{0}: {1}'?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Parametre değiştiricileri yalnızca TypeScript dosyalarında kullanılabilir.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "Parametrenin --isolatedDeclarations içeren açık bir tür ek açıklaması olmalıdır.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Dışarı aktarılan sınıftaki genel ayarlayıcı '{0}' için parametre türü, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Dışarı aktarılan sınıftaki genel ayarlayıcı '{0}' için parametre türü, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Dışarı aktarılan sınıftaki genel statik ayarlayıcı '{0}' için parametre türü, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Dışarı aktarılan sınıftaki genel statik ayarlayıcı '{0}' için parametre türü, '{1}' özel adına sahip veya bu adı kullanıyor.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "<PERSON><PERSON> modda ayrıştırın ve her kaynak dosya için \"use strict\" kullanın.", "Part_of_files_list_in_tsconfig_json_1409": "tsconfig.json içindeki 'files' listesinin par<PERSON>ı", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "'{0}' deseni en fazla bir adet '*' karakteri içerebilir.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "'--diagnostics' veya '--extendedDiagnostics' için performans zamanlamaları bu oturumda kullanılamaz. Web performans API'sinin yerel bir uygulaması bulunamadı.", "Platform_specific_6912": "<PERSON><PERSON>", "Prefix_0_with_an_underscore_90025": "'{0}' i<PERSON><PERSON> ön ek olarak alt çizgi kullan", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Hatalı tüm özellik bildirimlerinin başına 'declare' e<PERSON>in", "Prefix_all_unused_declarations_with_where_possible_95025": "Mümkün olduğunda tüm kullanılmayan bildiri<PERSON>in başına '_' ekle", "Prefix_with_declare_95094": "<PERSON><PERSON><PERSON><PERSON> '<PERSON>' e<PERSON><PERSON>", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "JavaScript çıktısında, içeri aktarılmazsa kaldırılacak olan kullanılmayan içe aktarılan değerleri koruyun.", "Print_all_of_the_files_read_during_the_compilation_6653": "<PERSON><PERSON><PERSON> sı<PERSON>ında okunan tüm dosyaları yazdırın.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "<PERSON><PERSON><PERSON>, neden dahil edildiğini de içerecek şekilde okunan dosyaları yazdırın.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "<PERSON><PERSON>a ad<PERSON>nı ve bunların derlemenin bir parçası olmalarının nedenini yazdır.", "Print_names_of_files_part_of_the_compilation_6155": "Derlemenin parçası olan dosyaların adlarını yazdırın.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Derlemenin parçası olan dosyaların adlarını yazdırın ve sonra işlemeyi durdurun.", "Print_names_of_generated_files_part_of_the_compilation_6154": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>enin parçası olanların adlarını yazdırın.", "Print_the_compiler_s_version_6019": "Derleyici sürümünü yazdır.", "Print_the_final_configuration_instead_of_building_1350": "<PERSON><PERSON><PERSON> yerine son ya<PERSON><PERSON><PERSON><PERSON>rmayı yazdırın.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "<PERSON><PERSON><PERSON>nda yayılan dosyaların adlarını yazdırın.", "Print_this_message_6017": "<PERSON>u iletiyi yazdır.", "Private_accessor_was_defined_without_a_getter_2806": "<PERSON>zel erişimci bir alıcı olmadan tanımlandı.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "<PERSON><PERSON> alan '{0}' ka<PERSON><PERSON>n sınıfta bildirilmelidir.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Değişken bildirimlerinde özel tanımlayıcılara izin verilmiyor.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Sın<PERSON>f gövdelerinin dışında özel tanımlayıcılara izin verilmiyor.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Özel tanımlayıcılara yalnızca sınıf gövdelerinde izin verilir ve yalnızca bir sınıf üyesi bildiriminin parçası olarak, özellik erişimi olarak veya ‘in’ ifadesinin sol tarafında kullanılabilirler.", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Özel tanımlayıcılar yalnızca ECMAScript 2015 veya üzeri hedeflenirken kullanılabilir.", "Private_identifiers_cannot_be_used_as_parameters_18009": "<PERSON>zel tanımlayıcılar parametre olarak kullanılamaz.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "Özel veya korumalı '{0}' ü<PERSON>ine bir tür parametresinde erişilemiyor.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "'{0}' proje<PERSON> zorla yeniden oluşturuluyor", "Project_0_is_out_of_date_because_1_6420": "Proje '{0}' g<PERSON><PERSON><PERSON> <PERSON><PERSON> {1}.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "Buildinfo dosyası '{1}', '{2}' dosyasının derlemenin kök dosyası iken artık öyle olmadığını belirttiğinden '{0}' projesi günce<PERSON>.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "Buildinfo dosyası '{1}' programın hataları bildirmesi gerektiğini belirttiğinden '{0}' projesi günce<PERSON> de<PERSON>.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "'{1}' buildinfo dosyası bazı değişikliklerin gösterilmediğini belirttiğinden '{0}' projesi günce<PERSON> de<PERSON>", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "Buildinfo dosyası '{1}' compilerOptions içinde değişiklik olduğunu belirttiğinden '{0}' proje adı güncel de<PERSON>", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "'{0}' projesinin '{1}' bağımlılığ<PERSON> güncel olmadığından proje güncel değil", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "'{1}' çı<PERSON><PERSON>ş<PERSON> '{2}' girişinden daha eski old<PERSON>n '{0}' projesi g<PERSON><PERSON><PERSON>", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "<PERSON><PERSON><PERSON><PERSON>ş dosyası '{1}' mevcut olmadığından '{0}' projesi günce<PERSON> de<PERSON>", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "'{0}' proje<PERSON><PERSON> geçerli '{2}' sürümünden farklı olan '{1}' s<PERSON>rü<PERSON>ü ile oluşturulduğundan proje güncel değil", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "'{1}' dosyası okunurken hata oluştuğu için '{0}' projesi gü<PERSON><PERSON>", "Project_0_is_up_to_date_6361": "'{0}' pro<PERSON><PERSON>l", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "En yeni '{1}' girişi '{2}' çıkışından daha eski olduğundan '{0}' proje<PERSON> g<PERSON>l", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "'{0}' proje<PERSON> güncel ancak giriş dosyalarından daha eski olan çıkış dosyalarına ait zaman damgalarının güncelleştirilmesi gerekiyor", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "'{0}' pro<PERSON><PERSON> b<PERSON>lıklarından d.ts dosyaları ile güncel", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Proje başvuruları döngüsel bir grafik formu oluşturamaz. Döngü tespit edildi: {0}", "Projects_6255": "<PERSON><PERSON><PERSON>", "Projects_in_this_build_Colon_0_6355": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> projeler: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "'accessor' değiştiricisine sahip özellikler yalnızca ECMAScript 2015 ve üzeri hedeflendiğinde kullanılabilir.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "'{0}' özelliği abstract olarak işaretlendiğinden bir başlatıcıya sahip olamaz.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "'{0}' özelliği bir dizin imzasından geldiğinden ['{0}'] ile erişilmelidir.", "Property_0_does_not_exist_on_type_1_2339": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{1}' t<PERSON><PERSON><PERSON><PERSON> değil.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "'{0}' <PERSON><PERSON><PERSON>ğ<PERSON> '{1}' türünde yok. Bunu mu demek istediniz: '{2}'?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "'{0}' <PERSON><PERSON><PERSON>ğ<PERSON> '{1}' türünde yok. Bunun yerine '{2}' statik üyesine erişmek mi istediniz?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "'{0}' özelliği '{1}' türünde yok. Hedef kitaplığınızı değiştirmeniz mi gerekiyor? 'lib' derleyici seçeneğini '{2}' veya üzeri olarak değiştirmeyi deneyin.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "'{0}' özelliği '{1}' türünde yok. '<PERSON><PERSON>' der<PERSON><PERSON> seç<PERSON> 'dom' içerecek şekilde <PERSON> den<PERSON>.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "'{0}' özelliği başlatıcı içermiyor ve kesinlikle bir sınıf statik bloğuna atanmamış.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "'{0}' özelliği başlatıcı içermiyor ve oluşturucuda kesin olarak atanmamış.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, get er<PERSON><PERSON><PERSON><PERSON><PERSON>n dönüş türü ek açıklaması olmadığı için örtük olarak 'any' türü içeriyor.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, set eriş<PERSON><PERSON>inin parametre türü ek açıklaması olmadığı için örtük olarak 'any' türü içeriyor.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "'{0}' özelliği örtük olarak 'any' türüne sahip ancak özelliğin get erişimcisi için kullanımdan daha iyi bir tür çıkarsanabilir.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "'{0}' özelliği örtük olarak 'any' türüne sahip ancak özelliğin set eriş<PERSON>cisi için kullanımdan daha iyi bir tür çıkarsanabilir.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "'{1}' türünde<PERSON> '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{2}' temel türündeki aynı özelliğe atanamaz.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "'{1}' tür<PERSON><PERSON><PERSON> '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{2}' tür<PERSON><PERSON> at<PERSON>.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "'{1}' tür<PERSON><PERSON><PERSON> '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{2}' türünün içinden erişilemeyen farklı bir üyeye başvuruyor.", "Property_0_is_declared_but_its_value_is_never_read_6138": "'{0}' özelliği bildirildi ancak değeri hiç okunmadı.", "Property_0_is_incompatible_with_index_signature_2530": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> imzasıyla uyumsuz.", "Property_0_is_missing_in_type_1_2324": "'{0}' özelliği '{1}' tü<PERSON><PERSON><PERSON> değ<PERSON>.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{1}' türünde eksik ancak '{2}' türünde gereklidir.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "'{0}' özelliğinin özel tanımlayıcısı olduğundan özelliğe '{1}' sınıfı dışında erişilemiyor.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{1}' türünde isteğe bağlıdır, ancak '{2}' türünde gereklidir.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "'{0}' özelliği özeldir ve yalnızca '{1}' sınıfı içinden erişilebilir.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, '{1}' türünde özel, '{2}' türünde özel değildir.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "'{0}' özelliği korunuyor ve yalnızca '{1}' sınıfının bir örneği üzerinden erişilebilir. Bu, '{2}' sınıfının bir örneğidir.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "'{0}' özelliği korumalıdır ve yalnızca '{1}' sınıfı içinden ve alt sınıflarından erişilebilir.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "'{0}' özelliği korumalıdır; ancak '{1}' türü, '{2}' öğesinden türetilmiş bir sınıf <PERSON>.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "'{0}' özelliği '{1}' türünde korumalı, '{2}' türünde ise ortaktır.", "Property_0_is_used_before_being_assigned_2565": "'{0}' özelliği atanmadan önce kullanıldı.", "Property_0_is_used_before_its_initialization_2729": "'{0}' özelliği başlatılmadan önce kullanılıyor.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "'{0}' <PERSON><PERSON><PERSON>ğ<PERSON> '{1}' türü üzerinde olamaz. Şunu mu demek istediniz: '{2}'?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "JSX yayılma özniteliğine ait '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, hede<PERSON> atanamaz.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "Dışarı aktarılan anonim sınıf türünün '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, özel veya korumalı olmayabilir.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "Dışarı aktarılan arabirimin '{0}' özelliği, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "Dışarı aktarılan arabirimin '{0}' özelliği, '{1}' özel adına sahip veya bu adı kullanıyor.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "'{1}' tü<PERSON><PERSON><PERSON><PERSON> '{0}' <PERSON><PERSON>liği '{2}' dizin türüne '{3}' atanamaz.", "Property_0_was_also_declared_here_2733": "'{0}' özelliği de burada bildirildi.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "'{0}' özelliği '{1}' içindeki temel özelliğin üzerine yazacak. Bu bilerek yapılıyorsa bir başlatıcı ekleyin. <PERSON><PERSON><PERSON>, bir 'declare' değiştiricisi ekleyin veya gereksiz bildirimi kaldırın.", "Property_assignment_expected_1136": "Özellik ataması bekleniyor.", "Property_destructuring_pattern_expected_1180": "Özellik yok etme deseni bekleniyor.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "Özelliğin --isolatedDeclarations içeren açık bir tür ek açıklaması olmalıdır.", "Property_or_signature_expected_1131": "Özellik veya imza bekleniyor.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Özellik değeri yalnız<PERSON>, dize sabit değeri, say<PERSON><PERSON> sabit değer, 'true', 'false', 'null', nesne sabit değeri veya dizi sabit değeri olabilir.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "'ES5' he<PERSON><PERSON><PERSON><PERSON>, 'for-of' içindeki yinelenebilir öğeler için yayılma ve yok etmeye yönelik tam destek sağlayın.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Dışarı aktarılan sınıfın '{0}' genel metodu, {2} dış modülündeki '{1}' adına sahip veya bu adı kull<PERSON>ıyor, ancak adlandırılamıyor.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Dışarı aktarılan sınıfın '{0}' genel metodu, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Dışarı aktarılan sınıfın '{0}' genel metodu, '{1}' özel adına sahip veya bu adı kullanıyor.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "Dışarı aktarılan sınıfın '{0}' ortak özelliği, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "Dışarı aktarılan sınıfın '{0}' ortak özelliği, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "Dışarı aktarılan sınıfın '{0}' ortak özelliği, '{1}' özel adına sahip veya bu adı kullanıyor.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Dışarı aktarılan sınıfın '{0}' genel statik metodu, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, anca<PERSON> adlandırılamıyor.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Dışarı aktarılan sınıfın '{0}' genel statik metodu, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Dışarı aktarılan sınıfın '{0}' genel statik metodu, '{1}' özel adına sahip veya bu adı kullanıyor.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Dışarı aktarılan sınıfın '{0}' ortak statik özelliği, {2} dış modülündeki '{1}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Dışarı aktarılan sınıfın '{0}' ortak statik özelliği, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Dışarı aktarılan sınıfın '{0}' ortak statik özelliği, '{1}' özel adına sahip veya bu adı kullanıyor.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "'{0}' tam adının başında '@param {object} {1}' olmadan bu ada izin verilmiyor.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "İşlev parametresi okunmadığında hata oluştur.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Belirt<PERSON><PERSON> 'any' tür<PERSON>ne sahip ifade ve bildirimlerde hata oluştur.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ak 'any' türü i<PERSON>eren 'this' ifadelerinde hata tetikle.", "Range_out_of_order_in_character_class_1517": "Aralık karakter sınıfında sıranın dışında.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "'{0}' etkinken bir türü yeniden dışarı aktarmak için 'export type' kullanmak gerekir.", "React_components_cannot_include_JSX_namespace_names_2639": "React bileşenleri JSX ad alanı adlarını içeremez", "Redirect_output_structure_to_the_directory_6006": "Çıktı yapısını dizine yeniden yönlendir.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "TypeScript tarafından otomatik olarak yüklenen projelerin sayısını azaltın.", "Referenced_project_0_may_not_disable_emit_6310": "Başvurulan '{0}' proje<PERSON>, ya<PERSON><PERSON> devre dışı bırak<PERSON>.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Başvurulan proje '{0}' \"composite\": true ayarına sahip olmalıdır.", "Referenced_via_0_from_file_1_1400": "'{1}' dos<PERSON>ından '{0}' aracılığıyla başvuruldu", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "'--moduleResolution' 'node16' veya 'nodenext' olduğ<PERSON>, bağıl içe aktarma yollarının ECMAScript içe aktarmalarında açık dosya uzantılarına ihtiyacı vardır. İçe aktarma yoluna bir uzantı eklemeyi düşünün.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "Göreli içe aktarma yolları, '--moduleResolution' 'node16' veya 'nodenext' olduğunda ECMAScript içeri aktarmalarında açık dosya uzantılarına ihtiyaç duyar. Şunu mu demek istediniz: '{0}'?", "Remove_a_list_of_directories_from_the_watch_process_6628": "İzleme işleminden dizinlerin listesini kaldırın.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "İzleme modu işlemesinden bir dosya listesini kaldırın.", "Remove_all_unnecessary_override_modifiers_95163": "<PERSON><PERSON><PERSON> gere<PERSON> 'override' <PERSON><PERSON><PERSON>ş<PERSON>ric<PERSON><PERSON> kaldır", "Remove_all_unnecessary_uses_of_await_95087": "<PERSON><PERSON>m gereksiz 'await' kullanımlarını kaldırın", "Remove_all_unreachable_code_95051": "<PERSON><PERSON>m erişilemeyen kodları kaldır", "Remove_all_unused_labels_95054": "<PERSON><PERSON><PERSON><PERSON><PERSON> tüm etiketleri kaldır", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "İlgili sorunları olan tüm ok işlev gövdelerinden ayraçları kaldır", "Remove_braces_from_arrow_function_95060": "Ok işlevinden küme ayraçlarını kaldır", "Remove_braces_from_arrow_function_body_95112": "Ok işlevi gövdesinden küme ayraçlarını kaldır", "Remove_import_from_0_90005": "'{0}' öğesinden içeri aktarmayı kaldır", "Remove_override_modifier_95161": "'override' <PERSON>ğiştiricisini kaldır", "Remove_parentheses_95126": "<PERSON><PERSON><PERSON><PERSON> kaldır", "Remove_template_tag_90011": "Şablon etiketini kaldırın", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "TypeScript dil sunucusundaki JavaScript dosyaları için toplam kaynak kodu boyutuna yönelik 20 MB sınırını kaldırın.", "Remove_type_from_import_declaration_from_0_90055": "\"{0}\" içindeki içeri aktarma bildiriminden “type”ı kaldırın", "Remove_type_from_import_of_0_from_1_90056": "\"{1}\" içindeki “{0}” içe aktarımından “type”ı kaldırın", "Remove_type_parameters_90012": "<PERSON><PERSON><PERSON> parametrelerini kaldırın", "Remove_unnecessary_await_95086": "Gereksiz 'await' öğesini kaldırın", "Remove_unreachable_code_95050": "Erişilemeyen kodları kaldır", "Remove_unused_declaration_for_Colon_0_90004": "<PERSON><PERSON><PERSON><PERSON><PERSON> '{0}' bild<PERSON><PERSON> kaldırın.", "Remove_unused_declarations_for_Colon_0_90041": "'{0}' i<PERSON><PERSON> bild<PERSON> kaldırın", "Remove_unused_destructuring_declaration_90039": "Yapıyı bozan kullanılmayan bildirimi kaldır", "Remove_unused_label_95053": "<PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON> kaldır", "Remove_variable_statement_90010": "Değişken deyi<PERSON> kaldır", "Rename_param_tag_name_0_to_1_95173": "“{0}” “@param” etiket adın<PERSON> “{1}” olarak yeniden adlandırın", "Replace_0_with_Promise_1_90036": "'{0}' <PERSON><PERSON><PERSON><PERSON> 'Promise<{1}>' il<PERSON> <PERSON>", "Replace_all_unused_infer_with_unknown_90031": "<PERSON><PERSON><PERSON><PERSON><PERSON> tüm 'infer' <PERSON><PERSON><PERSON><PERSON> 'unknown' il<PERSON>", "Replace_import_with_0_95015": "İçeri aktarma işlemini '{0}' ile değ<PERSON>tirin.", "Replace_infer_0_with_unknown_90030": "'infer {0}' ö<PERSON><PERSON><PERSON> 'unknown' il<PERSON> <PERSON>", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "İşlevdeki tüm kod yolları bir değer döndürmediğinde hata bildir.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "switch deyiminde sonraki ifadelere geçiş ile ilgili hataları bildir.", "Report_errors_in_js_files_8019": ".js dosyalarındaki hataları bildirin.", "Report_errors_on_unused_locals_6134": "Kullanılmayan yerel öğelerdeki hataları bildirin.", "Report_errors_on_unused_parameters_6135": "Kullanılmayan parametrelerdeki hataları bildirin.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "Diğer araçların geçici olarak bildirim dosyaları oluşturabilmesi için dışarı aktarmalarda yeterli ek açıklama gerektir.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Öğe erişimlerini kullanmak için dizin imzalarından bildirilmemiş özellikler gerektirin.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Gerekli tür parametreleri, isteğe bağlı tür parametrelerini takip edemez.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "'{0}' m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '{1}' konumundaki önbellekte bulundu.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "'{0}' tür ba<PERSON><PERSON><PERSON> yönergesinin çözümlemesi '{1}' konumundaki önbellekte bulundu.", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "Göreli olmayan ad çözümlenemedi; npm kitaplığı için yapılandırma güncelleştirmesi gerektirip gerekmediğini görmek için modern Düğüm çözümleme özellikleri ile deneniyor.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "<PERSON><PERSON><PERSON><PERSON> olmayan ad çözümlenemedi; pro<PERSON><PERSON> ya<PERSON>ılandı<PERSON> güncelleştirmesi gerektirip gerektirmediğini görmek için '--moduleResolution bundler' ile deneniyor.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "'Keyof' <PERSON><PERSON><PERSON><PERSON> yalnız<PERSON> dize değerli özellik adlarına (say<PERSON>lar veya simgeler olmadan) çözümleyin.", "Resolved_under_condition_0_6414": "'{0}' koşulu altında çözümlendi.", "Resolving_in_0_mode_with_conditions_1_6402": "{0} modunda {1} koşullarıyla çözümleniyor.", "Resolving_module_0_from_1_6086": "======== '{0}' modülü '{1}' öğesinden çözümleniyor. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "'{0}' mod<PERSON><PERSON> adı, '{1}' - '{2}' temel url'sine göre çözümleniyor.", "Resolving_real_path_for_0_result_1_6130": "'{0}' i<PERSON><PERSON> ger<PERSON> yo<PERSON> çözümleniyor, sonu<PERSON>: '{1}'.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== '{0}' tür ba<PERSON><PERSON><PERSON> yö<PERSON><PERSON>i <PERSON>ü<PERSON>iyo<PERSON>, ka<PERSON>ayan dosya: '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "========  '{0}' tür ba<PERSON><PERSON><PERSON> yö<PERSON>gesi çö<PERSON>ümleniyor, ka<PERSON><PERSON><PERSON> dos<PERSON>: '{1}', kök dizini: '{2}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "========  '{0}' tür başvuru yönergesi çözümleniyor, içeren dosya '{1}', kök dizin ayarlanmadı. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "========  '{0}' tür ba<PERSON><PERSON><PERSON> yönergesi çözümleniyor, ka<PERSON><PERSON>n <PERSON> a<PERSON>lanmadı, kök dizin: '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "========  '{0}' tür ba<PERSON><PERSON><PERSON> yönergesi çözümleniyor, ka<PERSON><PERSON>n dosya ayarlanmadı, kök dizin ayarlanmadı. ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "Özel typeRoots belirten program için tür başvurusu yönergesi çözümleniyor, 'node_modules' klasöründe arama atlanıyor.", "Resolving_with_primary_search_path_0_6121": "Birincil arama yolu '{0}' kullanılarak çözümleniyor.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "'{0}' rest parametresi, <PERSON><PERSON><PERSON><PERSON> o<PERSON> 'any[]' tür<PERSON>ne sahip.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "'{0}' REST parametresi örtük olarak 'any[]' türüne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "Rest_types_may_only_be_created_from_object_types_2700": "Rest türleri yalnızca nesne türlerinden oluşturulabilir.", "Return_type_annotation_circularly_references_itself_2577": "Dönüş türü ek açıklaması döngüsel olarak kendine başvuruyor.", "Return_type_must_be_inferred_from_a_function_95149": "Dönüş türü bir işlevden çıkarsanmalıdır", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Dışarı aktarılan arabirimdeki çağrı imzasının dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Dışarı aktarılan arabirimdeki çağrı imzasının dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Dışarı aktarılan arabirimdeki oluşturucu imzasının dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Dışarı aktarılan arabirimdeki oluşturucu imzasının dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Oluşturucu imzasının dönüş türü, sınıfın örnek türüne atanabilir olmalıdır.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Dışarı aktarılan işlevin dönüş türü, '{1}' dış modülündeki '{0}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Dışarı aktarılan işlevin dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Dışarı aktarılan işlevin dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Dışarı aktarılan arabirimdeki dizin imzasının dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Dışarı aktarılan arabirimdeki dizin imzasının dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Dışarı aktarılan arabirimdeki metodun dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Dışarı aktarılan arabirimdeki metodun dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Dışarı aktarılan sınıftaki genel alıcı '{0}' i<PERSON><PERSON> dö<PERSON> türü, '{2}' dış modülündeki '{1}' adına sahip veya bu adı kullanıyor ancak adlandırılamıyor.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Dışarı aktarılan sınıftaki genel alıcı '{0}' i<PERSON><PERSON> dö<PERSON> türü, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Dışarı aktarılan sınıftaki genel alıcı '{0}' i<PERSON><PERSON> dö<PERSON> türü, '{1}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Dışarı aktarılan sınıftaki ortak metodun dönüş türü, '{1}' dış modülündeki '{0}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Dışarı aktarılan sınıftaki ortak metodun dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Dışarı aktarılan sınıftaki ortak metodun dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Dışarı aktarılan sınıftaki genel statik alıcı '{0}' i<PERSON><PERSON> dön<PERSON> türü, '{2}' dış modülündeki '{1}' adına sahip veya bu adı kullanıyor ancak adlandırılamıyor.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Dışarı aktarılan sınıftaki genel statik alıcı '{0}' i<PERSON>in dön<PERSON> türü, '{2}' özel modülündeki '{1}' adına sahip veya bu adı kullanıyor.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Dışarı aktarılan sınıftaki genel statik alıcı '{0}' i<PERSON><PERSON> dön<PERSON> türü, '{1}' özel adına sahip veya bu adı kullanıyor.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Dışarı aktarılan sınıftaki ortak statik metodun dönüş türü, '{1}' dış modülündeki '{0}' adına sahip veya bu adı kullanıyor, ancak adlandırılamıyor.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Dışarı aktarılan sınıftaki ortak statik metodun dönüş türü, '{1}' özel modülündeki '{0}' adına sahip veya bu adı kullanıyor.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Dışarı aktarılan sınıftaki ortak statik metodun dönüş türü, '{0}' özel adına sahip veya bu adı kullanıyor.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ümlemesinin yeniden kullanılması, çözülmedi.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ç<PERSON>ümlemesinin yeniden kullanılması, başarıyla '{3}' o<PERSON><PERSON> çözüldü.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ümlemesinin yeniden kullanılması, başarıyla Paket Kimliği '{4}' ile '{3}' olarak çözüldü.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Eski programın '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> çözümlemesinin yeniden kullanılması, çözülmedi.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Eski programın '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON>n çözümlemesinin yeniden kullanılması, başarıyla '{2}' olarak çözüldü.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Eski programın '{1}' üzerindeki '{0}' mod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> çözümlemesinin yeniden kullanılması, başarıyla Paket Kimliği '{3}' ile '{2}' olarak çözüldü.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, çözülmedi.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, başar<PERSON><PERSON> '{3}' olarak çözüldü.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "'{2}' konumundaki önbellekte bulunan '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, başar<PERSON><PERSON> Paket Kimliği '{4}' ile '{3}' olarak çözüldü.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Eski programın '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, çözülmedi.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Eski programın '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, başar<PERSON><PERSON> '{2}' olarak çözüldü.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Eski programın '{1}' üzerindeki '{0}' tür başvuru yönergesi çözümlemesinin yeniden kullanılması, başar<PERSON><PERSON> Paket Kimliği '{3}' ile '{2}' olarak çözüldü.", "Rewrite_all_as_indexed_access_types_95034": "Tümünü di<PERSON>lenmiş erişim türleri olarak yeniden yaz", "Rewrite_as_the_indexed_access_type_0_90026": "Dizine eklenmiş eri<PERSON>im tür<PERSON> '{0}' olarak ye<PERSON> yaz", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "'.ts', '.tsx', '.mts' ve '.cts' dosya uzantılarını, çık<PERSON>ş dosyalarındaki JavaScript eşdeğerlerine göreli içeri aktarma yollarında yeniden yazın.", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "Sol işlenen hiçbir zaman null olmadığından ?? sağ işlenenine ulaşılamıyor.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Kök dizin belirlenemiyor, birin<PERSON>l arama yolları atlanıyor.", "Root_file_specified_for_compilation_1427": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> beli<PERSON>ilen kök dosyası", "STRATEGY_6039": "STRATEJİ", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Projelerin artımlı derlenmesini sağlamak için .tsbuildinfo dosyalarını kaydedin.", "Saw_non_matching_condition_0_6405": "Eşleşmeyen koşul '{0}' g<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Scoped_package_detected_looking_in_0_6182": "Kapsamlı paket algılandı, '{0}' içinde aranıyor", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "<PERSON><PERSON> dönüş uzantıları için tüm üst node_modules dizinleri aranıyor: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "Tercih edilen uzantılar için tüm üst node_modules dizinleri aranıyor: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> bir veya daha fazla deyi<PERSON>", "Selection_is_not_a_valid_type_node_95133": "<PERSON><PERSON><PERSON> geç<PERSON>li bir tür düğü<PERSON><PERSON>", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Yayılan JavaScript için JavaScript dil sürümünü ayarlayın ve uyumlu kitaplık bildirimlerini ekleyin.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "TypeScript’ten ileti dilini ayarlayın. Bu, yaymayı etkilemez.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 'module' seçeneğini '{0}' o<PERSON><PERSON> a<PERSON>layın", "Set_the_newline_character_for_emitting_files_6659": "Dosyaları yaymak için yeni satır karakterini ayarlayın.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Ya<PERSON><PERSON><PERSON><PERSON>rma <PERSON> 'target' seçeneğini '{0}' o<PERSON>ak a<PERSON>layın", "Setters_cannot_return_a_value_2408": "Ayarlayıcılar bir değer dö<PERSON>üremez.", "Show_all_compiler_options_6169": "<PERSON><PERSON><PERSON> der<PERSON> seçeneklerini gösterin.", "Show_diagnostic_information_6149": "Tanılama bilgilerini gösterin.", "Show_verbose_diagnostic_information_6150": "Ayrıntılı tanılama bilgilerini gösterin.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "<PERSON><PERSON><PERSON> (veya '--clean' ile belirtil<PERSON>ş<PERSON>) göster", "Signature_0_must_be_a_type_predicate_1224": "'{0}' <PERSON><PERSON><PERSON> bir tür koşulu olmalıdır.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "İmza bildirimleri yalnızca TypeScript dosyalarında kullanılabilir.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "Yukarı akış projesinde hata nedeniyle aşağı akış projelerini derlemeyi atla.", "Skip_type_checking_all_d_ts_files_6693": "Tüm .d.ts dosyalarında tür denetimini atlayın.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "TypeScript ile birlikte gelen .d.ts dosyaları için tür denetimini atlayın.", "Skip_type_checking_of_declaration_files_6012": "<PERSON><PERSON><PERSON><PERSON>ın tür denetimini atla.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "Mutlak bir URI gibi görünen '{0}' mod<PERSON><PERSON> tü<PERSON><PERSON>, hede<PERSON> <PERSON><PERSON> tü<PERSON>: {1}.", "Source_from_referenced_project_0_included_because_1_specified_1414": "'{1}' be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, baş<PERSON><PERSON>lan '{0}' proje<PERSON><PERSON> ka<PERSON> dahil edildi", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "'--module' 'none' o<PERSON><PERSON> be<PERSON>, baş<PERSON><PERSON><PERSON> '{0}' projesinin ka<PERSON> dahil edildi", "Source_has_0_element_s_but_target_allows_only_1_2619": "Kaynakta {0} öğe var ancak hedef yalnız<PERSON> {1} öğeye izin veriyor.", "Source_has_0_element_s_but_target_requires_1_2618": "Kaynakta {0} öğe var ancak hedef {1} öğe gerektiriyor.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "Kaynak, hedefteki {0} konumunda bulunan gerekli öğe için eşleşme sağlamıyor.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "Kaynak, hedefteki {0} konumunda bulunan değişen sayıda bağımsız değişken içeren öğe için eşleşme sağlamıyor.", "Specify_ECMAScript_target_version_6015": "ECMAScript hedef sü<PERSON><PERSON><PERSON> beli<PERSON>in.", "Specify_JSX_code_generation_6080": "JSX kodu oluşturmayı belirtin.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Tüm çıktıları tek bir JavaScript dosyasında paketleyen bir dosya belirt. 'declaration' de<PERSON><PERSON> true ise, tüm .d.ts çıktısını paketleyen bir dosya da belirtir.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Derlemeye dahil edilecek dosyalarla eşleşen glob desenlerinin bir listesini beli<PERSON>in.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Dahil edilecek dil hizmeti eklentilerinin listesini belirtin.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "<PERSON><PERSON><PERSON>ma zamanı ortamını açıklayan bir paket kitaplık bildirim dosyası kümesi belirtin.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "İçeri aktarmaları ek arama konumlarına yeniden eşleyen bir girdi kümesi belirtin.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "<PERSON><PERSON><PERSON> i<PERSON>in yolları belirten nesnelerin bir dizisini belirtin. Proje başvurularında kullanılır.", "Specify_an_output_folder_for_all_emitted_files_6678": "Tüm yayılan dosyalar için bir çıkış klasörü belirtin.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Yalnızca türler için kullanılan içeri aktarmalar için üretme/denetleme davranışını belirt.", "Specify_file_to_store_incremental_compilation_information_6380": "Artımlı derleme bilgilerinin depolanacağı dosyayı belirtin", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "TypeScript’in belirli bir modül belirticisinden bir dosyayı nasıl arayacağını belirtin.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Özyinelemeli dosya izleme işlevselliği olmayan sistemlerde dizinlerin nasıl izleneceğini belirtin.", "Specify_how_the_TypeScript_watch_mode_works_6715": "TypeScript izleme modunun nasıl çalıştığını belirtin.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Derlemeye dahil edilecek kitaplık dosyalarını belirtin.", "Specify_module_code_generation_6016": "Modül kodu oluşturmayı belirtin.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "'jsx: react-jsx*' kullanırken JSX fabrika işlevlerini içeri aktarmak için kullanılan modül belirticisini belirt.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "'./node_modules/@types' gibi davranan birden çok klasör belirt.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Ayarlarının dev<PERSON>ındığı temel yapılandırma dosyalarına yönelik bir veya daha fazla yol ya da düğüm modülü baş<PERSON><PERSON>u belirtin.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Bil<PERSON>im dosyalarının otomatik olarak alınması için seçenekleri belirtin.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Do<PERSON>a sistemi olayları kullanılarak oluşturulamadığında yoklama izlemesi oluşturma stratejisini belirtin: 'FixedInterval' (varsayılan), 'PriorityInterval', 'DynamicPriority', 'FixedChunkSize'.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Özyinelemeli izlemeyi yerel olarak desteklemeyen platformlarda dizini izleme stratejisini belirtin: 'UseFsEvents' (varsayılan), 'FixedPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling'.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "<PERSON><PERSON><PERSON> izle<PERSON> stratejisini belirtin: 'FixedPollingInterval' (varsayılan), 'PriorityPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling', 'UseFsEvents', 'UseFsEventsOnParentDirectory'.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "React JSX yayma hedeflenirken parçalar için kullanılacak JSX Parça baş<PERSON><PERSON><PERSON><PERSON> beli<PERSON>, örneğin 'React.Fragment' veya 'Fragment'.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "'React.createElement' veya 'h' gibi 'react' JSX emit hedeflerken kullanılacak JSX fabrika işlevini belirtin.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "React JSX üretme hedeflenirken kullanılacak JSX fabrika işlevini belirtin; örneğin 'React.createElement' veya 'h'.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "'jsxFactory' der<PERSON><PERSON> seçeneği belirtilmiş olarak 'react' JSX yaymasını hedeflerken kullanılacak JSX parçası fabrika işlevini belirtin (ör. 'Fragment').", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "G<PERSON><PERSON>i olmayan modül adlarını çözümlemek için temel dizini belirtin.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Dosyalar gösterilirken kullanılacak satır sonu dizisini belirtin: 'CRLF' (dos) veya 'LF' (unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Hata ayıklayıcının TypeScript dosyalarını kaynak konumlar yerine nerede bulması gerektiğini belirtin.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Hata ayı<PERSON>, eşlem dosyalarını üretilen konumlar yerine nerede bulması gerektiğini belirtin.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "'node_modules' öğesinden JavaScript dosyaları teslim almak için kullanılan maksimum klasör derinliğini belirtin. Yalnızca 'allowJs' ile geçerlidir.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "'jsx' ve 'jsxs' fabrika işlevlerini içeri aktarmak için kullanılacak modül tanımlayıcısını (ör. react) belirtin.", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "'createElement' i<PERSON><PERSON> ç<PERSON>ğrılan nesneyi belirtin. Bu, yaln<PERSON>z<PERSON> 'react' JSX üretme hedeflenirken geçerlidir.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Oluşturulan bildirim dosyaları için çıkış dizinini belirtin.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": ".tsbuildinfo artımlı derleme dos<PERSON>ının yolunu belirtin.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "<PERSON><PERSON><PERSON>yalarının kök dizinini belirtin. Çıkış dizininin yapısını --outDir ile denetlemek için kullanın.", "Specify_the_root_folder_within_your_source_files_6690": "Kaynak dosyalarınızda kök klasörü belirtin.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Başvuru kaynak kodunu bulmak için hata ayıklayıcıların kök yolunu belirtin.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Bir kaynak dosyada başvurulmadan eklenecek tür paketi adlarını belirtin.", "Specify_what_JSX_code_is_generated_6646": "Oluşturulacak JSX kodunu belirtin.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Sistemde yerel dosya izleyicileri tükenirse izleyicinin kullanması gereken yaklaşımı belirtin.", "Specify_what_module_code_is_generated_6657": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modül kodunu belirtin.", "Split_all_invalid_type_only_imports_1367": "Geçersiz tüm yalnızca tür içeri aktarmalarını bölün", "Split_into_two_separate_import_declarations_1366": "İki ayrı içeri aktarma bildirimine bölün", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "'new' ifadelerindeki yayılma işleci yalnızca ECMAScript 5 ve üzeri hedeflenirken kullanılabilir.", "Spread_types_may_only_be_created_from_object_types_2698": "<PERSON><PERSON><PERSON>lma türleri yalnızca nesne türlerinden oluşturulabilir.", "Starting_compilation_in_watch_mode_6031": "<PERSON><PERSON><PERSON>, i<PERSON>me modunda başlatılıyor...", "Statement_expected_1129": "<PERSON><PERSON><PERSON>.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Çevresel ba<PERSON><PERSON><PERSON>a deyimlere izin verilmez.", "Static_members_cannot_reference_class_type_parameters_2302": "Statik üyeler sınıf türündeki parametrelere başvuramaz.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "'{0}' statik özelliği, '{1}' oluşturucu işlevinin yerleşik özelliği olan 'Function.{0}' ile çakışıyor.", "String_literal_expected_1141": "<PERSON>ze sabit de<PERSON>eri bekle<PERSON>.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "'--module' bayra<PERSON><PERSON> 'es2015' veya 'es2020' o<PERSON><PERSON>landığında, sabit de<PERSON><PERSON>li dize içeri ve dışarı aktarma adları desteklenmez.", "String_literal_with_double_quotes_expected_1327": "Çift tırnak içine alınmış bir dize sabit değeri bekleniyor.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Renk ve bağlam kullanarak hataların ve iletilerin stilini beli<PERSON> (deneysel).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "Eksi işareti varken alt sayfa bayrakları bulunmalıdır.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Ardışık özellik bildirimleri aynı türe sahip olmalıdır. '{0}' özelliği '{1}' türünde olmalıdır, ancak burada '{2}' türüne sahip.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Ardışık değişken bildirimleri aynı türe sahip olmalıdır. '{0}' değişkeni '{1}' türünde olmalıdır, ancak burada '{2}' türüne sahip.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "'{1}' deseni i<PERSON> '{0}' alternatifinin türü hatalı; beklenen: 'string' alınan: '{2}'.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "'{1}' desenindeki '{0}' alternatifi en fazla bir '*' karakteri içerebilir.", "Substitutions_for_pattern_0_should_be_an_array_5063": "'{0}' des<PERSON> i<PERSON> bir dizi olmalıdır.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "'{0}' des<PERSON> boş bir dizi olamaz.", "Successfully_created_a_tsconfig_json_file_6071": "tsconfig.json dosyası başarıyla oluşturuldu.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Super çağrılarına oluşturucu dışında veya oluşturucu içindeki iç içe işlevlerde izin verilmez.", "Suppress_excess_property_checks_for_object_literals_6072": "Nesne sabit değerlerine ait fazla özellik denetimlerini gösterme.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Dizin imzaları olmayan nesneler için dizin oluştururken noImplicitAny hatalarını gösterme.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Dizin imzası olmayan nesnelerin dizinini oluştururken 'noImplicitAny' hatalarını gizle.", "Switch_each_misused_0_to_1_95138": "Yanlış kullanılan tüm '{0}' öğelerini '{1}' <PERSON><PERSON><PERSON>ğiştir", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Geri çağırmaları eşzamanlı olarak çağırın ve özyinelemeli izlemeyi yerel olarak desteklemeyen platformlardaki dizin izleyicilerinin durumunu güncelleştirin.", "Syntax_Colon_0_6023": "<PERSON><PERSON><PERSON> dizimi: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "'{0}' etiketi en az '{1}' bağ<PERSON>ms<PERSON>z değişken bekliyor, ancak '{2}' JSX fabrikası en fazla '{3}' tane sağlıyor.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "İsteğe bağlı bir zincirde etiketli şablon ifadelerine izin verilmiyor.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "<PERSON><PERSON><PERSON> {0} öğeye izin veriyor ancak kaynakta daha fazlası olabilir.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "Hedef {0} öğe gerektiriyor ancak kaynakta daha az sayıda öğe olabilir.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "<PERSON><PERSON><PERSON> imza çok az bağımsız değişken sağlıyor. {0} veya daha fazla bekleniyordu ancak {1} alındı.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "'{0}' değiştiricisi yalnızca TypeScript dosyalarında kullanılabilir.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "'{0}' i<PERSON><PERSON><PERSON>, 'symbol' t<PERSON><PERSON><PERSON><PERSON>.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "'{0}' işlecine boole türü için izin verilmez. Bunun yerine '{1}' kullanmayı göz önünde bulundurun.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "Asenkron bir yineleyicinin '{0}' özelliği bir metot olmalıdır.", "The_0_property_of_an_iterator_must_be_a_method_2767": "Yineleyicinin '{0}' özelliği bir metot olmalıdır.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "'Object' türü başka çok az sayıda türe atanabilir. Bunun yerine 'any' türünü mü kullanmak istemiştiniz?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "Unicode (u) bayrağı ve Unicode Kümeleri (v) bayrağı aynı anda ayarlanamaz.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "ES5'te bulunan bir ok işlevinde 'arguments' nesnesine başvurulamaz. Standart bir işlev ifadesi kullanmayı göz önünde bulundurun.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "'arguments' nesnesine ES5'teki as<PERSON><PERSON>ron bir işlev veya metotta başvurulamaz. Standart bir işlev veya metot kullanmayı düşü<PERSON>ün.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "'if' deyi<PERSON>n g<PERSON> bo<PERSON> deyim o<PERSON>az.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bu uygulamada başar<PERSON><PERSON>ı o<PERSON>di, ancak aşırı yüklemelerin uygulama imzaları dışarıdan görünmüyor.", "The_character_set_of_the_input_files_6163": "<PERSON><PERSON><PERSON>arının karakter kümesi.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "Ka<PERSON>ayıcı ok işlevi, 'this' öğesinin genel değerini yakalar.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "İçeren işlev veya modül gövdesi, denetim akışı analizi için çok büyük.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Geçerli dosya bir CommonJS modülüdür ve en üst düzeyde 'await'i kullanamaz.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Geç<PERSON>li dosya, içe aktarma işlemlerinin 'require' çağrıları üreteceği bir CommonJS modülüdür; ancak başvurulan dosya bir ECMAScript modülüdür ve 'require' ile içe aktarılamaz. Bunun yerine dinamik bir 'import(\"{0}\")' çağrısı yazmayı deneyin.", "The_current_host_does_not_support_the_0_option_5001": "Mevcut ana bilgis<PERSON>r '{0}' se<PERSON><PERSON><PERSON><PERSON> desteklemiyor.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "Büyük olasılıkla kullanmayı amaçladığınız '{0}' bild<PERSON><PERSON> burada tanı<PERSON>r", "The_declaration_was_marked_as_deprecated_here_2798": "<PERSON><PERSON><PERSON>im burada kullanım dışı olarak işaretlendi.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "<PERSON><PERSON><PERSON> tür, burada '{1}' türünde bildirilen '{0}' özelliğinden geliyor", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Beklenen tür bu imzanın dönüş türünden geliyor.", "The_expected_type_comes_from_this_index_signature_6501": "Beklenen tür bu dizin imzasından geliyor.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "Bir dışarı aktarma ataması ifadesi, çevresel bağlamda bir tanımlayıcı veya tam ad olmalıdır.", "The_file_is_in_the_program_because_Colon_1430": "<PERSON><PERSON><PERSON>, <PERSON>u ne<PERSON> programın içinde:", "The_files_list_in_config_file_0_is_empty_18002": "'{0}' ya<PERSON><PERSON><PERSON><PERSON><PERSON> dos<PERSON> 'dosyalar' list<PERSON> boş.", "The_first_export_default_is_here_2752": "İlk dışarı aktarma varsayılanı buradadır.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Promise'in 'then' metodunun ilk parametresi, bir geri arama parametresi olmalıdır.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "'JSX.{0}' genel türü birden fazla özelliğe sahip olamaz.", "The_implementation_signature_is_declared_here_2750": "Uygulama imzası burada bildirilir.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "CommonJS çıkışında oluşturulacak dosyalarda 'import.meta' meta özelliğine izin verilmiyor.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "'import.meta' meta özelliğine yalnızca '--module' seçeneği 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18' veya 'nodenext' olduğunda izin verilir.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "'{0}' <PERSON><PERSON><PERSON><PERSON>, '{1}' ba<PERSON><PERSON><PERSON>u olmadan adlandırılamaz. Bu büyük olasılıkla taşınabilir değildir. Tür ek açıklaması gereklidir.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Çıkarsanan '{0}' t<PERSON><PERSON><PERSON>, önemsiz olarak seri hale getirilemeyen döngüsel yapıya sahip bir türe başvurur. Tür ek açıklaması gerekir.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Çıkarsanan '{0}' tür<PERSON>, erişilemeyen bir '{1}' tür<PERSON>ne başvuruyor. Tür ek açıklaması gereklidir.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "<PERSON><PERSON> dü<PERSON><PERSON><PERSON><PERSON><PERSON> tü<PERSON>, derleyicinin seri hale getireceği maksimum uzunluğu aşıyor. Açık tür ek açıklaması gerekiyor.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "'using' bildiri<PERSON>n başlatıcısı ya '[Symbol.dispose]()' metoduna sahip bir nesne olmalı ya da 'null' veya 'undefined' olmal<PERSON>d<PERSON>r.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "'await using' bildiri<PERSON>n başlatı<PERSON>ısı ya '[Symbol.asyncDispose]()' veya '[Symbol.dispose]5D;()' metoduna sahip bir nesne olmalı ya da 'null' veya 'undefined' olmalıd<PERSON>r.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "'{1}' özelliği birden çok destekçide bulunduğundan ve bazılarında özel olduğundan, '{0}' kesişimi 'never' de<PERSON><PERSON><PERSON>.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "'{1}' özelliği bazı bileşenlerde çakışan türlere sahip olduğundan '{0}' kesişimi 'never' de<PERSON><PERSON><PERSON>.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "'intrinsic' anahtar s<PERSON>, yalnızca derleyicinin sağladığı iç türleri bildirmek için kullanılabilir.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "JSX parçalarının 'jsxFactory' derleyici seçeneği ile kullanılabilmesi için 'jsxFragmentFactory' derleme seçeneği belirtilmelidir.", "The_last_overload_gave_the_following_error_2770": "Son aşırı yükleme aşağıdaki hatayı verdi.", "The_last_overload_is_declared_here_2771": "Son aşırı yükleme burada bildirilir.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "'for...in' deyiminin sol tarafı yok etme deseni olamaz.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "'for...in' ifadesinin sol tarafı 'using' bildirimi olamaz.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "'for...in' ifadesinin sol tarafı 'await using' bildirimi olamaz.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "'for...in' deyiminin sol tarafında tür ek açıklaması kullanılamaz.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "'for...in' deyiminin sol tarafı, isteğe bağlı bir özellik erişimi olamaz.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "'for...in' deyiminin sol tarafında bir değişken veya özellik erişimi bulunmalıdır.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "'for...in' deyiminin sol tarafı 'string' veya 'any' tür<PERSON>nde olmalıdır.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "'for...of' deyiminin sol tarafında tür ek açıklaması kullanılamaz.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "'for...of' deyiminin sol tarafı, isteğe bağlı bir özellik erişimi olamaz.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "'for...of' deyiminin sol tarafı, asenkron olamaz.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "'for...of' deyiminin sol tarafında bir değişken veya özellik erişimi bulunmalıdır.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "Aritmetik işlemin sol tarafı, 'any', 'number', 'bigint' veya bir sabit listesi türünde olmalıdır.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "Atama ifadesinin sol tarafı, isteğe bağlı bir özellik erişimi olamaz.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "Atama ifadesinin sol tarafında bir değişken veya özellik erişimi bulunmalıdır.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "Bir 'instanceof' ifadesinin sol tarafı, sağ tarafın '[Symbol.hasInstance]' metodunun ilk bağımsız değişkenine atanabilir olmalıdır.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "'instanceof' if<PERSON><PERSON>n sol tarafı 'any' tü<PERSON><PERSON><PERSON>, bir nesne türü veya tür parametresi olmalıdır.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Kullanıcıya ileti görüntülenirken kullanılacak yerel ayar (örn. 'tr-tr')", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "node_modules altında arama yapmak ve JavaScript dosyalarını yüklemek için en yüksek bağımlılık derinliği.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "'delete' operatörünün işleneni özel bir tanımlayıcı olamaz.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "'delete' operat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, salt okunur bir özellik olamaz.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "'delete' opera<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir özellik ba<PERSON><PERSON><PERSON>u olmalıdır.", "The_operand_of_a_delete_operator_must_be_optional_2790": "'delete' operatörünün işleneni isteğe bağlı olmalıdır.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "Artırma veya eksiltme operatörünün i<PERSON>ni, is<PERSON><PERSON>e bağlı bir özellik erişimi o<PERSON>az.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "Artırma veya eksiltme operatörünün <PERSON>ni, bir değişken veya özellik erişimi olmalıdır.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, buradaki '{0}' belirteciyle eşleştirmek için bir '{1}' bulmayı bekliyordu.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "Proje kö<PERSON>ü beli<PERSON>izdir, ancak '{1}' dosyasındaki '{0}' dışa aktarma haritası girişini çözmek için gereklidir. Belirsizliği gidermek için `rootDir` derleyici seçeneğini sağlayın.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "Proje kö<PERSON>ü beli<PERSON>, ancak '{1}' dosyasındaki '{0}' içe aktarma haritası girişini çözmek için gereklidir. Belirsizliği gidermek için `rootDir` derleme seçeneğini sağlayın.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "'{0}' özelliği aynı yazımı içeren başka bir özel tanımlayıcı tarafından gölgelendiğinden bu sınıf içindeki '{1}' türü üzerinde bu özelliğe erişilemiyor.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Parametre dekoratör işlevine ait dönüş türü 'void' veya 'any' olmalıdır.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Özellik dekoratör işlevine ait dönüş türü 'void' veya 'any' olmalıdır.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Zaman uyumsuz bir işlevin dönüş türü, ge<PERSON><PERSON>li bir promise olmalı veya çağrılabilir 'then' üyesi içermemelidir.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "Zaman uyumsuz bir işlevin ya da metodun döndürme türü, genel Promise<T> tür<PERSON> olmalıdı<PERSON>.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Asenkron bir i<PERSON>levin ya da metodun dönüş türü, genel Promise<T> türü olmalıdır. 'Promise<{0}>' yaz<PERSON>k mı istediniz?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "'for...in' deyiminin sağ tarafı 'any' tür<PERSON><PERSON>, bir nesne türü veya tür parametresi olmalıdır ancak burada '{0}' tür<PERSON>ne sahip.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "Aritmetik işlemin sağ tarafı, 'any', 'number', 'bigint' veya bir sabit listesi türünde olmalıdır.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "'instanceof' if<PERSON><PERSON>n sağ tarafı ya 'any' t<PERSON><PERSON><PERSON><PERSON>, bir <PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON> veya 'Function' arayüz türüne atanabilir başka bir tür ya da 'Symbol.hasInstance' metoduna sahip bir nesne türü olmalıdır.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "'instanceof' ifadesinin sağ tarafı bir örnek oluşturma ifadesi olmamalıdır.", "The_root_value_of_a_0_file_must_be_an_object_5092": "'{0}' dos<PERSON><PERSON><PERSON><PERSON>n kök değeri bir nesne olmalıdır.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "Çalışma zamanı, dekoratörü {1} bağımsız değişkenleriyle çağıracak ancak dekoratör {0} bekliyor.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "Çalışma zamanı, dekoratörü {1} bağımsız değişkenleriyle çağıracak ancak dekoratör en azından {0} bekliyor.", "The_shadowing_declaration_of_0_is_defined_here_18017": "'{0}' i<PERSON><PERSON> bildirimi burada tanı<PERSON>r", "The_signature_0_of_1_is_deprecated_6387": "'{1}' öğ<PERSON>nin '{0}' <PERSON><PERSON>ı kullanım dışı bırakıldı.", "The_specified_path_does_not_exist_Colon_0_5058": "<PERSON><PERSON><PERSON>n yol yok: '{0}'.", "The_tag_was_first_specified_here_8034": "Etiket ilk olarak burada belirtildi.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "Nesne REST atamasını<PERSON> he<PERSON>, iste<PERSON>e bağlı bir özellik erişimi olamaz.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "Nesne geri kalan özellik atamasının he<PERSON>, bir değişken veya özellik erişimi olmalıdır.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "'{0}' türü<PERSON><PERSON><PERSON> 'this' ba<PERSON><PERSON><PERSON>, metodun '{1}' türü<PERSON><PERSON> 'this' de<PERSON><PERSON><PERSON>.", "The_this_types_of_each_signature_are_incompatible_2685": "İmzalar<PERSON><PERSON> 'this' tü<PERSON>ri uyumsuz.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "'{0}' tür<PERSON> 'readonly' <PERSON><PERSON><PERSON><PERSON><PERSON> değiştirilebilir '{1}' tür<PERSON><PERSON> at<PERSON>mıyo<PERSON>.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Dışarı aktarma deyimindeki 'dışarı aktarma türü' kullanılırken 'tür' değiştiricisi adlandırılmış bir dışarı aktarma üzerinde kullanılamaz.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "İçeri aktarma deyimindeki 'içeri aktarma türü' kullanılırken 'tür' değiştiricisi adlandırılmış bir içeri aktarma üzerinde kullanılamaz.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "İşlev bildiriminin türü işlevin imzasıyla eşleşmelidir.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "'{0}' özelliği seri hale getirilemediğinden bu düğüm türü seri hale getirilemiyor.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Asenkron yineleyicinin '{0}()' metodu tarafından döndür<PERSON><PERSON> tür, 'value' özelliğine sahip bir tür için promise olmalıdır.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Bir yineleyicinin '{0}()' metodu tarafından döndü<PERSON><PERSON>len tür, 'value' özelliğine sahip olmalıdır.", "The_types_of_0_are_incompatible_between_these_types_2200": "'{0}' tü<PERSON><PERSON> bu türler a<PERSON> uyumsuz.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "'{0}' ta<PERSON><PERSON><PERSON><PERSON><PERSON> dö<PERSON><PERSON><PERSON><PERSON> türle<PERSON>, bu türler a<PERSON>ı<PERSON> uyumsuz.", "The_value_0_cannot_be_used_here_18050": "'{0}' <PERSON><PERSON><PERSON> b<PERSON> k<PERSON>.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "'for...in' deyi<PERSON>n değişken bildirimi bir başlatıcıya sahip olamaz.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "'for...of' deyi<PERSON>n değişken bildirimi bir başlatıcıya sahip olamaz.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "'with' if<PERSON><PERSON>. 'with' bloklarında<PERSON> tüm simgeler 'any' tür<PERSON>ne sa<PERSON> olacaktır.", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "'{0}' konumunda türler var, ancak bu sonuç geçerli 'moduleResolution' ayarınız altında çözümlenemiyor. 'node16', 'nodenext' veya 'bundler' öğesine güncelleştirmeyi düşünün.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "'{0}' konumunda türler var ancak package.json \"exports\" dikkate alındığında bu sonuç çözümlenemedi. '{1}' kitaplığının package.json dosyasını veya türlerini güncelleştirmesi gerekiyor olabilir.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "Bu normal ifadede '{0}' adlı yakalama grubu yok.", "There_is_nothing_available_for_repetition_1507": "Yineleme için k<PERSON>anılabilecek bir şey yok.", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "Bu JSX etiketi '{0}' öğesinin kapsamda olmasını gerektiriyor, ancak bulunamadı.", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "Bu JSX etiketi, '{0}' mod<PERSON>l yolunun mevcut olmasını gerektiriyor, ancak hiçbiri bulunamadı. Uygun paket için türlerin yüklü olduğundan emin olun.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Bu JSX etiketinin '{0}' <PERSON><PERSON><PERSON>ğ<PERSON>, '{1}' türünde tek bir alt öğe bekliyor ancak birden çok alt öğe sağlandı.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Bu JSX etiketinin '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>, birden çok alt öğe gerektiren '{1}' tür<PERSON><PERSON>ü bekliyor ancak yalnızca tek bir alt öğe sağlandı.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "Bu geri ba<PERSON><PERSON><PERSON>, var olmayan bir gruba başvuruyor. Bu normal ifadede yalnızca yakalama grupları yok.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "Bu geri ba<PERSON><PERSON><PERSON>, var olmayan bir gruba başvuruyor. Bu normal ifadede yalnızca {0} yakalama grubu var.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "Bu ikili ifade hiçbir zaman boş değerli değildir. Parantezler mi eksik?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "Bu karakter düzenli bir ifadede kaçış işareti ile kullanılamaz.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "'{0}' ve '{1}' türle<PERSON><PERSON> ç<PERSON>ış<PERSON> olmadığından bu karşılaş<PERSON><PERSON>rma yanlışlıkla yapılmış gibi görünüyor.", "This_condition_will_always_return_0_2845": "<PERSON><PERSON> ko<PERSON>ul her zaman '{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "JavaScript nesneleri değer göre değil başvuruya göre karşılaştırdığından bu koşul her zaman '{0}' de<PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>ü<PERSON>.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Bu '{0}' her zaman tanımlandığı için bu koşul her zaman doğru olacaktır.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "<PERSON><PERSON><PERSON> her zaman tanımlı olduğundan bu koşul her zaman true döndürür. Bunun yerine işlevi çağırmayı mı istediniz?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Bu oluşturucu işlevi bir sınıf bildirimine dönüştürülebilir.", "This_expression_is_always_nullish_2871": "<PERSON><PERSON> <PERSON>ade her zaman bo<PERSON> değerlidir.", "This_expression_is_not_callable_2349": "Bu ifade çağrılabilir değil.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Bu ifade 'get' er<PERSON><PERSON><PERSON><PERSON>i olduğundan çağrılamaz. Bunu '()' o<PERSON><PERSON> mı kullanmak istiyorsunuz?", "This_expression_is_not_constructable_2351": "Bu ifade oluşturulabilir değil.", "This_file_already_has_a_default_export_95130": "Bu dosyanın zaten varsayılan bir dışarı aktarması var", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "Başka bir projeye çözümlendiğinden ve projelerin çıkış dosyaları arasındaki göreli yol, giriş dosyaları arasındaki göreli yol ile aynı olmadığından, bu içeri aktarma yolunun yeniden yazılması güvenli değildir.", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "Bu i<PERSON><PERSON> aktarma, giriş TypeScript dosyasına çözümlemek için bir '{0}' uzantısı kullanıyor, ancak göreli bir yol olmadığından yayın sırasında yeniden yazılmayacak.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "<PERSON><PERSON>, genişletilmekte olan bildirimdir. Genişleten bildirimi aynı dosyaya taşımayı düşünün.", "This_kind_of_expression_is_always_falsy_2873": "<PERSON><PERSON> <PERSON><PERSON> türü her zaman yanlıştır.", "This_kind_of_expression_is_always_truthy_2872": "<PERSON><PERSON> <PERSON><PERSON> türü her zaman doğrudur.", "This_may_be_converted_to_an_async_function_80006": "<PERSON><PERSON>, asenkron bir işleve dönüştürülebilir.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "<PERSON><PERSON> <PERSON><PERSON>, '{0}' temel sınıfında bildirilmediğinden '@override' et<PERSON><PERSON> olan bir JSDoc yorumuna sahip olamaz.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "<PERSON>u <PERSON><PERSON>, '{0}' temel sınıfında bildirilmediğinden 'override' etiketi olan bir JSDoc yorumuna sahip olamaz. '{1}' öğesini mi kastettiniz?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Ka<PERSON>ayan sınıfı '{0}' baş<PERSON> bir sınıfı genişletmediğinden, bu üye '@override' etiketi olan bir JSDoc yorumuna sahip olamaz.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "Ad dinamik olduğundan bu üye '@override' etiketi içeren bir JSDoc yorumuna sahip olamaz.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Bu üye '{0}' temel sınıfında bildirilmediğinden 'override' değiştiricisine sahip olamaz.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "<PERSON><PERSON> <PERSON><PERSON>, '{0}' temel sınıfında bildirilmediğinden 'override' değiştiricisine sahip olamaz. '{1}' öğesini mi kastettiniz?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Kapsayan '{0}' sı<PERSON><PERSON><PERSON><PERSON> başka bir sınıfı genişletmediğinden bu üye 'override' değiştiricisine sahip olamaz.", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "Adı dinamik olduğundan bu üyenin 'override' değiştiricisi olamaz.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "' {0}' temel sınıfındaki bir üyeyi geçersiz kıldığından, bu üyenin '@override' etike<PERSON> olan bir JSDoc yo<PERSON> o<PERSON>ıdı<PERSON>.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "<PERSON>u üye, '{0}' temel sınıfındaki bir üyeyi geçersiz kıldığından 'override' değiştiricisine sahip olmalıdır.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "<PERSON><PERSON> <PERSON><PERSON>, '{0}' temel sınıfında bildirilen soyut bir metodu geçersiz kıldığından 'override' değiştiricisine sahip olmalıdır.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Bu modüle yalnızca '{0}' bayrağını açıp modülün varsayılan dışarı aktarma işlemine başvurarak ECMAScript içeri/dışarı aktarma işlemleri ile başvurulabilir.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Bu modül, 'export =' ile bildirildi ve yalnızca '{0}' bayrağı kullanılırken varsayılan bir içeri aktarmayla kullanılabilir.", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "Bu işlem basitleştirilebilir. Bu vardiya `{0} {1} {2}` ile ayn<PERSON>.", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, dön<PERSON>ş türü ek açıklaması içermediğinden '{0}' türünü örtük olarak döndürür.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Bu aşır<PERSON> yükleme imzası, uygulama imzasıyla uyumlu değil.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Bu parametreye 'use strict' yönergesi ile izin verilmiyor.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "' {0}' temel sınıfındaki bir üyeyi geçersiz kıldığından, bu parametre özelliğinin '@override' et<PERSON><PERSON> olan bir JSDoc yorumu olmalıdır.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Bu parametre özelliği, '{0}' temel sınıfındaki bir üyeyi geçersiz kıldığından bir 'override' değiştiricisi içermelidir.", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "Bu normal ifade bayrağı bir alt örüntü içinde değiştirilemez.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "Bu normal ifade bayrağı, yaln<PERSON>z<PERSON> '{0}' veya üzeri hedeflenirken kullanılabilir.", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "Bu göreli içeri aktarma yolu bir dosya adı gibi görünse de aslında \"{0}\" olarak çözümlendiğinden yolun yeniden yazılması güvenli değildir.", "This_spread_always_overwrites_this_property_2785": "<PERSON>u yayılma her zaman bu özelliğin üzerine yazar.", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "'erasableSyntaxOnly' etkinleştirildiğinde bu söz dizimi kullanılamaz.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Bu söz dizimi, .mts veya .cts uzantısı içeren dosyalarda ayrılmıştır. Sonuna virgül veya açık kısıtlama ekleyin.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "<PERSON>u söz dizimi, .mts veya .cts uzantısı içeren dosyalarda ayrılmıştır. <PERSON><PERSON><PERSON> yerine `as` if<PERSON><PERSON> k<PERSON>.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "<PERSON><PERSON>ö<PERSON> dizi<PERSON>, içeri aktarılan bir yardımcı gerektiriyor ancak '{0}' mod<PERSON><PERSON>ü bulunamıyor.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "<PERSON><PERSON> söz dizimi, '{0}' içinde bulunmayan '{1}' adlı içeri aktarılmış bir yardımcı gerektirir. '{0}' sürümünüzü yükseltmeyi deneyin.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "<PERSON>u söz dizimi, '{0}' içindeki yardımcı ile uyumlu olmayan, {2} parametreye sahip '{1}' adlı içeri aktarılan yardımcıyı gerektiriyor. '{0}' sürümünüzü yükseltmeyi düşünün.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Bu tür parametresinin bir `extends {0}` kısıtlamasına ihtiyacı olabilir.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "'import' çağrısının bu kullanımı geçersiz. 'import()' çağrıları yazılabilir ancak ayraç içermeleri gerekir ve tür bağımsız değişkenleri içeremezler.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Bu dosyayı bir ECMAScript modülüne dönüştürmek için, '{0}' dizinine `\"type\": \"module\"` al<PERSON><PERSON> e<PERSON>.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Bu dosyayı bir ECMAScript modülüne dönüştürmek için dosya uzantısını '{0}' olarak değiştirin veya '{1}' dizinine ''type': 'module'' al<PERSON><PERSON> e<PERSON>.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Bu dosyayı bir ECMAScript modülüne dönüştürmek için dosya uzantısını '{0}' olarak değiştirin veya `{ \"type\": \"module\" }` ile yerel bir package.json dosyası oluşturun.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Bu dosyayı bir ECMAScript modülüne dönüştürmek için, `{ \"type\": \"module\" }` ile yerel bir package.json dosyası oluşturun.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Üst düzey 'await' ifadelerine yalnızca 'module' seçeneğ<PERSON>; 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında ve 'target' seçeneği; 'es2017' veya üzeri olarak ayarlandığında izin verilir.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "Üst düzey 'await using' ifadelerine yalnızca 'module' seçeneğ<PERSON>; 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında ve 'target' seçeneği; 'es2017' veya üzeri olarak ayarlandığında izin verilir.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": ".d.ts dosyalarındaki üst düzey bildirimler bir 'declare' veya 'export' değiştiricisi ile başlamalıdır.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Üst düzey 'for await' döngülerine yalnızca 'module' seçeneğ<PERSON>; 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' veya 'preserve' olarak ayarlandığında ve 'target' seçeneği; 'es2017' veya üzeri olarak ayarlandığında izin verilir.", "Trailing_comma_not_allowed_1009": "Sona eklenen virgüle izin verilmez.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Her dosyayı ayrı bir mod<PERSON>l <PERSON> ('ts.transpileModule' gibi).", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Varsa `npm i --save-dev @types/{1}` deneyin veya `declare module '{0}';` deyimini içeren yeni bir bildirim (.d.ts) dosyası ekleyin", "Trying_other_entries_in_rootDirs_6110": "'rootDirs' içindeki diğer girişler deneniyor.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "'{0}' alternat<PERSON><PERSON> den<PERSON>, aday modül konumu: '{1}'.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "'{1}' u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '{0}' demet türü, '{2}' dizininde öğe içermiyor.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Demet türü bağımsız değişkenleri döngüsel olarak kendisine başvuruyor.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "'{0}' tür<PERSON> yaln<PERSON> '--downlevelIteration' bayrağı kullanılarak veya '--target' için 'es2015' ya da üzeri kullanıldığında yinelenebilir.", "Type_0_cannot_be_used_as_an_index_type_2538": "'{0}' t<PERSON><PERSON><PERSON>, dizin türü olarak kull<PERSON>ı<PERSON>.", "Type_0_cannot_be_used_to_index_type_1_2536": "'{0}' tür<PERSON>, '{1}' tür<PERSON><PERSON><PERSON> dizinlemek için kullanı<PERSON>az.", "Type_0_does_not_satisfy_the_constraint_1_2344": "'{0}' tü<PERSON><PERSON>, '{1}' k<PERSON><PERSON><PERSON>tlamasını karşılamıyor.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "'{0}' tür<PERSON>, beklenen '{1}' tür<PERSON><PERSON><PERSON> kar<PERSON>yor.", "Type_0_has_no_call_signatures_2757": "'{0}' tü<PERSON><PERSON><PERSON><PERSON><PERSON> çağrı imzası yok.", "Type_0_has_no_construct_signatures_2761": "'{0}' tü<PERSON><PERSON><PERSON><PERSON><PERSON> oluşturma imzası yok.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "'{0}' tür<PERSON>, '{1}' tü<PERSON><PERSON><PERSON> e<PERSON> dizin imzasına sa<PERSON> değil.", "Type_0_has_no_properties_in_common_with_type_1_2559": "'{0}' türünün '{1}' tür<PERSON><PERSON> ortak özelliği yok.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "'{0}' t<PERSON><PERSON><PERSON>, tür ba<PERSON><PERSON><PERSON><PERSON><PERSON> değişkeni listesi için geçerli imzalar içermiyor.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "Tür '{0}' geneldir ve yalnızca okuma için dizini oluşturulabilir.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "'{0}' tür<PERSON><PERSON>, '{1}' türündeki şu özellikler eksik: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "'{0}' tü<PERSON><PERSON><PERSON>, '{1}' türündeki şu özellikler eksik: {2} ve diğer {3} özellik.", "Type_0_is_not_a_constructor_function_type_2507": "'{0}' tür<PERSON> bir oluşturucu işlevi tür<PERSON> de<PERSON>.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "ES5 içindeki '{0}' <PERSON><PERSON><PERSON><PERSON>, Promise ile uyumlu bir oluşturucu değerine başvurmadığından geçerli bir zaman uyumsuz işlev dönüş türü değil.", "Type_0_is_not_an_array_type_2461": "'{0}' tür<PERSON> bir dizi tür<PERSON>.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "'{0}' tü<PERSON><PERSON>, bir dizi türü veya dize türü de<PERSON>.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "'{0}' tü<PERSON><PERSON>, bir dizi türü veya dize türü değil ya da bir yineleyici döndüren '[Symbol.iterator]()' metoduna sahip değil.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "'{0}' tü<PERSON><PERSON>, bir dizi türü değil ya da bir yineleyici döndüren '[Symbol.iterator]()' metoduna sahip değil.", "Type_0_is_not_assignable_to_type_1_2322": "'{0}' türü, '{1}' tür<PERSON><PERSON>.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "'{0}' türü '{1}' tür<PERSON>ne atana<PERSON>. '{2}' mi demek isted<PERSON>z?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "'{0}' türü '{1}' tür<PERSON>ne atanamaz. Bu ada sahip iki farklı tür mevcut, ancak bu türler birb<PERSON><PERSON>.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Varyans ek açıklaması tarafından belirtildiği gibi '{0}' türü '{1}' tür<PERSON><PERSON> atanamaz.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "Hesaplanan sabit listesi üye değerleri gereğince, '{0}' türü '{1}' tür<PERSON><PERSON> at<PERSON>.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "'{0}' türü, '{1}' türüne 'exactOptionalPropertyTypes: true' ile atanamaz. Hedef özelliklerinin türlerine 'undefined' e<PERSON><PERSON><PERSON> deneyin.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "'{0}' türü, '{1}' türü<PERSON> 'exactOptionalPropertyTypes: true' ile atanamaz. Hedef türüne 'undefined' e<PERSON><PERSON><PERSON> deneyin.", "Type_0_is_not_comparable_to_type_1_2678": "'{0}' türü '{1}' tü<PERSON><PERSON><PERSON>.", "Type_0_is_not_generic_2315": "'{0}' t<PERSON><PERSON><PERSON> genel de<PERSON>.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "'{0}' tipi, 'in' operatörünün doğru işleneni olarak izin verilmeyen temel bir değeri temsil edebilir.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zaman uyumsuz bir yine<PERSON>ici döndüren bir '[Symbol.asyncIterator]()' metoduna sahip olması gerekir.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "'{0}' t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir y<PERSON><PERSON><PERSON> döndüren '[Symbol.iterator]()' metoduna sahip olması gerekir.", "Type_0_provides_no_match_for_the_signature_1_2658": "'{0}' tü<PERSON><PERSON>, '{1}' <PERSON><PERSON><PERSON> için eşleşme sağlamıyor.", "Type_0_recursively_references_itself_as_a_base_type_2310": "'{0}' t<PERSON><PERSON><PERSON>, <PERSON><PERSON> y<PERSON>lemeli şekilde kendine temel tür olarak başvuruyor.", "Type_Checking_6248": "<PERSON><PERSON><PERSON>", "Type_alias_0_circularly_references_itself_2456": "'{0}' t<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> olarak kendine başvuruyor.", "Type_alias_must_be_given_a_name_1439": "<PERSON><PERSON>r takma adına bir ad verilmesi gerekir.", "Type_alias_name_cannot_be_0_2457": "<PERSON><PERSON><PERSON> adı '{0}' olamaz.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "<PERSON><PERSON><PERSON>, yalnızca TypeScript dosyalarında kullanılabilir.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Tür ek açıklaması, oluşturucu bildiriminde görüntülenemez.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Tür ek açıklamaları yalnızca TypeScript dosyalarında kullanılabilir.", "Type_argument_expected_1140": "<PERSON><PERSON><PERSON> bağ<PERSON><PERSON><PERSON>z değişkeni bekleniyor.", "Type_argument_list_cannot_be_empty_1099": "<PERSON><PERSON><PERSON> bağ<PERSON>ms<PERSON>z değişkeni listesi boş olamaz.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "<PERSON><PERSON>r bağımsız değişkenleri yalnızca TypeScript dosyalarında kullanılabilir.", "Type_arguments_for_0_circularly_reference_themselves_4109": "'{0}' <PERSON><PERSON><PERSON> tür ba<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> olarak kendisine başvuruyor.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "<PERSON><PERSON><PERSON>, yalnızca TypeScript dosyalarında kullanılabilir.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Kaynaktaki {0} konumunda bulunan tür, hedefteki {1} konumunda bulunan türle uyumlu değil.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Kaynaktaki {0} ile {1} arasındaki konumlarda bulunan tür, hedefteki {2} konumunda bulunan türle uyumlu değil.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "'{0}' özel adını içeren tür, --isolatedDeclarations ile kullanılamaz.", "Type_declaration_files_to_be_included_in_compilation_6124": "Derlemeye eklenecek tür bildirim dosyaları.", "Type_expected_1110": "<PERSON><PERSON><PERSON>.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Tür içe aktarma iddiaları, `import` veya `require`. değerine sahip tam olarak bir anahtara - `resolution-mode`’na sahip olmalıdır.", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "Tür içe aktarma öznitelikleri, 'import' veya 'require' de<PERSON><PERSON><PERSON>, tam olarak tek bir 'resolution-mode' anahtarına sahip olmalıdır.", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "Bir ECMAScript modülünün CommonJS modülünden tür içeri aktarımında bir 'resolution-mode' özniteliği bulunmalıdır.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "<PERSON><PERSON>r örneği oluşturma işlemi, fazla ayrıntılı ve büyük olasılıkla sınırsız.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "<PERSON><PERSON><PERSON>, kendi 'then' metod<PERSON>un tamamlama geri arama<PERSON>ında doğ<PERSON>an veya dolaylı olarak başvuruluyor.", "Type_library_referenced_via_0_from_file_1_1402": "'{1}' dos<PERSON>ından '{0}' aracılığıyla başvurulan tür kitaplığı", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "'{2}' paket kim<PERSON> sahip '{1}' dosyasından '{0}' aracılığıyla başvurulan tür kitaplığı", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "'await' <PERSON><PERSON><PERSON><PERSON><PERSON>, ge<PERSON><PERSON><PERSON> bir promise olmalı veya çağrılabilir 'then' üyesi içermemelidir.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Hesaplanan özellik değerinin '{0}' türü, '{1}' tür<PERSON><PERSON> atanamıyor.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "'{0}' örnek üyesi değişkeninin türü, oluşturucuda bildirilen '{1}' tanımlayıcısına başvuramaz.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Bir 'yield*' i<PERSON><PERSON>ninin yinelenen öğelerinin türü, geç<PERSON>li bir promise olmalı veya çağrılabilir 'then' üyesi içermemelidir.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tür<PERSON>, '{1}' eşlenmiş türünde döngüsel olarak kendine başvuruyor.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Zaman uyumsuz bir oluşturucudaki 'yield' işleneninin türü, geç<PERSON>li bir promise olmalı veya çağrılabilir 'then' üyesi içermemelidir.", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "Bir ECMAScript modülünün bir CommonJS modülünden yalnızca tür içeri aktarımında bir 'resolution-mode' özniteliği bulunmalıdır.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Tür bu içeri aktarmadan kaynaklanıyor. Ad alanı stili içeri aktarma işlemi çağrılamaz ya da oluşturulamaz ve çalışma zamanında hataya neden olur. Bunun yerine varsayılan içeri aktarmayı kullanabilir veya burada içeri aktarma gerektirebilirsiniz.", "Type_parameter_0_has_a_circular_constraint_2313": "'{0}' tür parametresi döngüsel bir kısıtlamaya sahip.", "Type_parameter_0_has_a_circular_default_2716": "'{0}' tür parametresi döngü<PERSON> bir var<PERSON><PERSON><PERSON>.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Dışarı aktarılan arabirimdeki çağrı imzasının '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Dışarı aktarılan arabirimdeki oluşturucu imzasının '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Dışarı aktarılan sınıfın '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Dışarı aktarılan işlevin '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Dışarı aktarılan arabirimin '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Dışarı aktarılmış eşlenen nesne türüne ait '{0}' tür parametresi, '{1}' özel adını kullanıyor.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Dışarı aktarılan tür diğer adına ait '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Dışarı aktarılan arabirimdeki metodun '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Dışarı aktarılan sınıftaki ortak metodun '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Dışarı aktarılan sınıftaki ortak statik metodun '{0}' tür parametresi, '{1}' özel adına sahip veya bu adı kullanıyor.", "Type_parameter_declaration_expected_1139": "Tür parametresi bildirimi bekleniyor.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Tür parametresi bildirimleri yalnızca TypeScript dosyalarında kullanılabilir.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Tür parametresi varsayılanları yalnızca önceden bildirilen tür parametrelerine başvurabilir.", "Type_parameter_list_cannot_be_empty_1098": "Tür parametresi listesi boş o<PERSON>.", "Type_parameter_name_cannot_be_0_2368": "Tür parametresi adı '{0}' olamaz.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bildiri<PERSON> gör<PERSON><PERSON><PERSON><PERSON>z.", "Type_predicate_0_is_not_assignable_to_1_1226": "'{0}' t<PERSON><PERSON> k<PERSON>, '{1}' <PERSON><PERSON><PERSON><PERSON>.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "<PERSON><PERSON><PERSON>, temsil edilemeyecek kadar büyük olan bir demet türü oluşturuyor.", "Type_reference_directive_0_was_not_resolved_6120": "======== '{0}' tür ba<PERSON><PERSON><PERSON> yönergesi çözümlenmedi. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== '{0}' tür baş<PERSON>ru yönerges<PERSON> '{1}' olar<PERSON> başar<PERSON><PERSON>, birincil: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== '{0}' tür başvuru yönergesi '{2}' Paket <PERSON> sa<PERSON> '{1}' olarak başarıyla <PERSON>, birincil: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "T<PERSON>r karşılama ifadeleri yalnızca TypeScript dosyalarında kullanılabilir.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "<PERSON><PERSON><PERSON><PERSON>, JavaScript dosyalarında dışarı aktarma bildirimlerinde görünemez.", "Types_have_separate_declarations_of_a_private_property_0_2442": "<PERSON><PERSON><PERSON><PERSON>, '{0}' özel özelliğinin ayrı bildirimlerine sahip.", "Types_of_construct_signatures_are_incompatible_2419": "Yapı imzalarının türleri uyumsuz.", "Types_of_parameters_0_and_1_are_incompatible_2328": "'{0}' ve '{1}' parametre türleri uyu<PERSON>.", "Types_of_property_0_are_incompatible_2326": "'{0}' özellik türleri uyumsuz.", "Unable_to_open_file_0_6050": "'{0}' dos<PERSON>ı açılamıyor.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Bir ifade olarak çağrıldığında sınıf dekoratörünün imzası çözümlenemez.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Bir ifade olarak çağrıldığında metot dekoratörünün imzası çözümlenemez.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Bir ifade olarak çağrıldığında parametre dekoratörünün imzası çözümlenemez.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Bir ifade olarak çağrıldığında özellik dekoratörünün imzası çözümlenemez.", "Undetermined_character_escape_1513": "Belirlenmemiş karakter kaçışı.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "Beklenmeyen '{0}'. Ters eğik çizgiyle bundan kaçmak mı istediniz?", "Unexpected_end_of_text_1126": "Beklenmeyen metin sonu.", "Unexpected_keyword_or_identifier_1434": "Beklenmeyen anahtar kelime veya tanımlayıcı.", "Unexpected_token_1012": "Beklenmeyen belirteç.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Beklenmeyen belirteç. B<PERSON>, metot, er<PERSON>ş<PERSON>ci veya özellik bekleniyordu.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Beklenmeyen belirteç. Küme ayracı olmadan bir tür parametresi adı bekleniyordu.", "Unexpected_token_Did_you_mean_or_gt_1382": "Beklenmeyen belirteç. Şunu mu demek istediniz: `{'>'}` veya `&gt;`?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Beklenmeyen belirteç. Şunu mu demek istediniz: `{'}'}` veya `&rbrace;`?", "Unexpected_token_expected_1179": "Beklenmeyen belirteç. '{' bekleniyordu.", "Unicode_escape_sequence_cannot_appear_here_17021": "Unicode kaçış dizisi burada görünemez.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "Unicode kaçış dizileri yalnızca Unicode (u) bayrağı veya Unicode Kümeleri (v) bayrağı ayarlandığında kullanılabilir.", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "Unicode özellik değeri ifadeleri yalnızca Unicode (u) bayrağı veya Unicode Kümeleri (v) bayrağı ayarlandığında kullanılabilir.", "Unknown_Unicode_property_name_1524": "Bilinmeyen Unicode özelliği adı.", "Unknown_Unicode_property_name_or_value_1529": "Bilinmeyen Unicode özelliği adı veya değeri.", "Unknown_Unicode_property_value_1526": "Bilinmeyen Unicode özelliği değeri.", "Unknown_build_option_0_5072": "Bilinmeyen '{0}' der<PERSON><PERSON>.", "Unknown_build_option_0_Did_you_mean_1_5077": "Bilinmeyen '{0}' der<PERSON><PERSON>. Şunu mu demek istediniz: '{1}'?", "Unknown_compiler_option_0_5023": "Bilinmeyen '{0}' der<PERSON><PERSON> se<PERSON>.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Bilinmeyen '{0}' der<PERSON><PERSON> seçeneği. Şunu mu demek istediniz: '{1}'?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Bilinmeyen anahtar sözcük veya tanımlayıcı. “{0}” mi demek istediniz?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "'Excludes' se<PERSON><PERSON><PERSON><PERSON> bi<PERSON>. 'Exclude' se<PERSON><PERSON><PERSON><PERSON> mi belirtmek istediniz?", "Unknown_regular_expression_flag_1499": "Bilinmeyen normal ifade bayrağı.", "Unknown_type_acquisition_option_0_17010": "Bilinmeyen '{0}' tür alımı seçeneği.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Bilinmeyen '{0}' tür alma se<PERSON>ğ<PERSON>. Şunu mu demek istediniz: '{1}'?", "Unknown_watch_option_0_5078": "Bilinmeyen '{0}' <PERSON><PERSON><PERSON>.", "Unknown_watch_option_0_Did_you_mean_1_5079": "Bilinmeyen '{0}' i<PERSON><PERSON>. Şunu mu demek istediniz: '{1}'?", "Unreachable_code_detected_7027": "Erişilemeyen kod algılandı.", "Unterminated_Unicode_escape_sequence_1199": "Sonlandırılmamış Unicode kaçış dizisi.", "Unterminated_quoted_string_in_response_file_0_6045": "'{0}' yan<PERSON>t dos<PERSON>ında sonlandırılmamış alıntılanmış dize.", "Unterminated_regular_expression_literal_1161": "Sonlandırılmamış normal ifade sabit değeri.", "Unterminated_string_literal_1002": "Sonlandırılmamış dize sabit değeri.", "Unterminated_template_literal_1160": "Sonlandırılmamış şablon sabit değeri.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "T<PERSON>rü belirtilmemiş işlev çağrıları tür bağımsız değişkenlerini kabul etmeyebilir.", "Unused_label_7028": "<PERSON><PERSON><PERSON><PERSON><PERSON> etiket.", "Unused_ts_expect_error_directive_2578": "<PERSON><PERSON><PERSON><PERSON><PERSON> '@ts-expect-error' y<PERSON><PERSON><PERSON><PERSON>.", "Update_import_from_0_90058": "\"{0}\" kaynağından içeri aktarmayı güncelleştir", "Update_modifiers_of_0_90061": "'{0}' <PERSON><PERSON><PERSON>ş<PERSON>ricilerini güncelleştirin", "Updating_output_timestamps_of_project_0_6359": "'{0}' proje<PERSON><PERSON> zaman damgaları güncelleştiriliyor...", "Updating_unchanged_output_timestamps_of_project_0_6371": "'{0}' proje<PERSON><PERSON> çıkış zaman damgaları güncelleştiriliyor...", "Use_0_95174": "`{0}` k<PERSON><PERSON><PERSON>n.", "Use_0_instead_5106": "<PERSON><PERSON>un yerine '{0}' k<PERSON><PERSON><PERSON>n.", "Use_Number_isNaN_in_all_conditions_95175": "Tüm koşullarda `Number.isNaN` kullanın.", "Use_element_access_for_0_95145": "'{0}' i<PERSON><PERSON>an", "Use_element_access_for_all_undeclared_properties_95146": "Tüm bildirilmemiş özellikler için öğe erişimi kull<PERSON>ın.", "Use_import_type_95180": "'İçeri aktarma türü' kullanın", "Use_synthetic_default_member_95016": "Yapay 'default' <PERSON><PERSON><PERSON> k<PERSON>n.", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "Paket içe aktarmalarını çözümlerken package.json 'exports' alanını kullanın.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "İçeri aktarmaları çözümlerken package.json dosyasındaki 'imports' alanını kullanın.", "Use_type_0_95181": "'type {0}' k<PERSON><PERSON><PERSON>n", "Using_0_subpath_1_with_target_2_6404": "Hedef '{2}' ile '{0}' alt yol '{1}' kullan<PERSON>lıyor.", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "JSX parçalarının kullanımı '{0}' parça fabrikasının kapsamda olmasını gerektiriyor, ancak bulunamadı.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "'for...of' deyi<PERSON>e dize kullanma yalnızca ECMAScript 5 veya üzerinde desteklenir.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "--build kullanarak -b, tsc’nin derleyici yerine derleme düzenleyici gibi davranmasına yol açar. Bu, kompozit projeler oluşturmayı tetiklemek için kullanılır. Daha fazla bilgi edinmek için bkz. {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "'{0}' proje ba<PERSON><PERSON>u yeniden yönlendirmesinin derleyici seçenekleri kullanılıyor.", "VERSION_6036": "SÜRÜM", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "'{0}' tür<PERSON><PERSON><PERSON><PERSON> de<PERSON> ile '{1}' türü arasında hiç ortak özellik yok. Bunun yerine çağrı yapmak mı istediniz?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "'{0}' türündeki değeri çağrılabilir değil. 'new' öğesini mi eklemek istemiştiniz?", "Variable_0_implicitly_has_an_1_type_7005": "'{0}' <PERSON>ğ<PERSON>şkeni örtük olarak '{1}' tü<PERSON><PERSON><PERSON> sahip.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "'{0}' değişkeni örtük olarak bir '{1}' türüne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "'{0}' <PERSON>ğ<PERSON>şkeni bazı konumlarda örtük olarak '{1}' tür<PERSON>ne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tü<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>me<PERSON>ceği bazı konumlarda örtülü olarak '{1}' tür<PERSON><PERSON><PERSON> içeriyor.", "Variable_0_is_used_before_being_assigned_2454": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>i atanmadan önce kullanılır.", "Variable_declaration_expected_1134": "Değişken bildirimi be<PERSON>ni<PERSON>.", "Variable_declaration_list_cannot_be_empty_1123": "Değişken bildirim listesi boş olamaz.", "Variable_declaration_not_allowed_at_this_location_1440": "Bu konumda değişken bildirimine izin verilmiyor.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "Değişkende --isolatedDeclarations içeren açık bir tür ek açıklaması olmalıdır.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "Birden çok bildirimi olan <PERSON>kenler satır içine alınamaz.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "Kaynaktaki {0} konumunda bulunan değişen sayıda bağımsız değişken içeren öğe, hedefteki {1} konumunda bulunan öğeyle eşleşmiyor.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Varyans ek açıklamaları yalnızca ne<PERSON>ne, <PERSON><PERSON><PERSON>, oluşturucu ve eşlenen türler için tür diğer adlarında desteklenir.", "Version_0_6029": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Bu dosya hakkında daha fazla bilgi için https://aka.ms/tsconfig sayfasını ziyaret edin", "WATCH_OPTIONS_6918": "İZLEME SEÇENEKLERİ", "Watch_and_Build_Modes_6250": "İzleme ve Derleme Modları", "Watch_input_files_6005": "<PERSON><PERSON><PERSON> dosyalarını izleyin.", "Watch_option_0_requires_a_value_of_type_1_5080": "'{0}' <PERSON><PERSON><PERSON>, {1} tür<PERSON>nde bir değer gerektiriyor.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "'{0}' i<PERSON>in bir türü yalnızca tüm parametre için buraya bir tür ekleyerek yazabiliriz.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "İşlevler atanırken, parametrelerin ve dönüş değerlerinin alt tür ile uyumlu olduğundan emin olun.", "When_type_checking_take_into_account_null_and_undefined_6699": "<PERSON><PERSON><PERSON> denetimi sı<PERSON>nda 'null' ve 'undefined' <PERSON><PERSON><PERSON><PERSON> hesaba kat.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Eski konsol çıktısının ekrandan kaldırılmak yerine izleme modunda tutulup tutulmayacağı.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Geçersiz tüm karakterleri bir ifade kapsayıcısında sarmalayın", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "Tüm geçersiz dekoratör ifadelerini parantez içine alın", "Wrap_all_object_literal_with_parentheses_95116": "Tüm nesne sabit değerini parantez içine alın", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "JSX parçasındaki tüm ana öğesiz JSX'leri sarmala", "Wrap_in_JSX_fragment_95120": "JSX parçasında sarmala", "Wrap_in_parentheses_95194": "Parantez içine al<PERSON>n", "Wrap_invalid_character_in_an_expression_container_95108": "Geçersiz karakteri bir ifade kapsayıcısında sarmalayın", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Nesne sabit değeri olması gereken aşağıdaki gövdeyi parantez içine alın", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "<PERSON><PERSON><PERSON> derleyici seçenekleri hakkında bilgi edinmek için bkz. {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Genel içeri aktarma aracılığıyla bir modülü yeniden adlandıramazsınız.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Bir 'node_modules' klasöründe tanımlanan öğeler yeniden adlandırılamaz.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Başka bir 'node_modules' klasöründe tanımlanan öğeler yeniden adlandırılamaz.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Standart TypeScript kitaplığında tanımlanmış öğeleri yeniden adlandıramazsınız.", "You_cannot_rename_this_element_8000": "Bu öğeyi yeniden adlandıramazsınız.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' burada dekoratör olarak kullanılmak için çok az bağımsız değişken kabul ediyor. Önce çağırıp '@{0}()' yazmak mı istediniz?", "_0_and_1_index_signatures_are_incompatible_2330": "'{0}' ve '{1}' dizin imzaları uyumsuz.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "'{0}' ve '{1}' işlemleri ayraç olmadan karıştırılamaz.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' iki kez beli<PERSON>. '{0}' özniteliğinin üzerine yazılacak.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "'{0}' bir türün sonunda geçerli TypeScript sözdizimi değildir. '{1}' yazmak mı istemiştiniz?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "'{0}' bir türün başlangıcında geçerli TypeScript sözdizimi değildir. '{1}' yazmak mı istemiştiniz?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}' yaln<PERSON>zca 'esModuleInterop' bayrağı etkinleştirilip varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}' ya<PERSON><PERSON><PERSON><PERSON> varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}' yalnızca 'require' çağrısı kullanılarak veya 'esModuleInterop' bayrağı etkinleştirilip varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}' yalnızca 'require' çağrısı veya varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}' yalnızca 'import {1} = require({2})' veya varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}' yalnızca 'import {1} = require({2})' kullanılarak veya 'esModuleInterop' bayrağı etkinleştirilip varsayılan içeri aktarma kullanılarak içeri aktarılabilir.", "_0_cannot_be_used_as_a_JSX_component_2786": "'{0}', JSX bileşeni olarak kullanılamaz.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "'{0}', 'export type' kullanılarak dışarı aktarıldığından değer olarak kullanılamaz.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "'{0}', 'import type' kullanılarak içeri aktarıldığından değer olarak kullanılamaz.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, alt öğe olarak metin kabul etmez. JSX'teki metin 'string' türünde ancak beklenen '{1}' türü: '{2}'.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "'{0}' <PERSON><PERSON><PERSON><PERSON>, '{1}' ile iliş<PERSON> olma<PERSON> rastgele bir türle oluşturulabilir.", "_0_declarations_can_only_be_declared_inside_a_block_1156": "'{0}' bildirimleri yalnızca bir bloğun içinde bildirilebilir.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "'{0}' bild<PERSON>mleri yalnızca TypeScript dosyalarında kullanılabilir.", "_0_declarations_may_not_have_binding_patterns_1492": "'{0}' bi<PERSON><PERSON><PERSON><PERSON><PERSON> bağlama desenleri olamaz.", "_0_declarations_must_be_initialized_1155": "'{0}' bi<PERSON><PERSON><PERSON><PERSON> b<PERSON>şlatılmalıdır.", "_0_expected_1005": "'{0}' be<PERSON><PERSON>yor.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "'{0}' bir dize türüne sahip ancak 'isolatedModules' etkinleştirildiğinde sözdizimsel olarak tanınabilir dize sözdizimi olmalıdır.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{0}' öğesinin dışarı aktarılan '{1}' adlı bir üyesi yok. '{2}' demek mi istediniz?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' örtük olarak bir '{1}' dön<PERSON>ş türüne sahip ancak kullanımdan daha iyi bir tür çıkarsanabilir.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "Dönüş türü ek açıklamasına sahip olmadığından ve doğrudan veya dolaylı olarak dönüş ifadelerinden birinde kendine başvurulduğundan, '{0}' öğesi örtük olarak 'any' türüne sahip.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "Bir tür ek açıklamasına sahip olmadığından ve kendi başlatıcısında doğrudan veya dolaylı olarak başvurulduğundan, '{0}' öğesi örtük olarak 'any' türüne sahip.", "_0_index_signatures_are_incompatible_2634": "'{0}' dizin imzaları uyumsuz.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "'{0}' dizin türü '{1}' ' {2}' dizin türüne '{3}' atanamaz.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' temel el<PERSON>ı<PERSON> anca<PERSON> '{1}' sarmalayıcı nesnedir. Mümkün olduğunda '{0}' kullanmayı tercih edin.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' bir tür ve JavaScript dosyalarında içeri aktarılamaz. Bir JSDoc türü ek açıklamasında '{1}' kullanın.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "'{0}' bir türdür ve 'verbatimModuleSyntax' etkinleştirildiğinde yalnızca tür içeri aktarması kullanılarak içeri aktarılmalıdır.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}', '{1}' tür<PERSON><PERSON><PERSON>n kullanılmayan bir yeniden adlandırması. Tür ek açıklaması olarak mı kullanmak istediniz?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}', '{1}' türündeki kısıtlamaya atanabilir ancak '{1}' <PERSON>rne<PERSON><PERSON>, '{2}' kıs<PERSON>tl<PERSON>sının farklı bir alt türüyle oluşturulabilir.", "_0_is_automatically_exported_here_18044": "'{0}' burada otomatik olarak dışarı aktarılır.", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' bi<PERSON><PERSON><PERSON>i ancak değeri hiç okunmadı.", "_0_is_declared_but_never_used_6196": "'{0}' bi<PERSON><PERSON><PERSON>i ancak hiç kullanılmadı.", "_0_is_declared_here_2728": "'{0}' b<PERSON>da bildirilir.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}', '{1}' sınıfında bir özellik olarak tanımlandı ancak burada, '{2}' içinde bir erişimci olarak geçersiz kılındı.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}', '{1}' sınıfında bir erişimci olarak tanımlandı ancak burada, '{2}' içinde örnek özelliği olarak geçersiz kılındı.", "_0_is_deprecated_6385": "'{0}' k<PERSON><PERSON><PERSON><PERSON> dışı bırakıldı.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}', '{1}' anahtar sözcüğü için geçerli bir meta özellik değil. Bunu mu demek istediniz: '{2}'?", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}' öğesine parametre adı olarak izin verilmiyor.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' ö<PERSON><PERSON>nin değişken bildirim adı olarak kullanılmasına izin verilmiyor.", "_0_is_of_type_unknown_18046": "'{0}' 'unknown' t<PERSON><PERSON><PERSON><PERSON>.", "_0_is_possibly_null_18047": "'{0}' de<PERSON><PERSON>nin 'null' o<PERSON><PERSON><PERSON> olasıdır.", "_0_is_possibly_null_or_undefined_18049": "'{0}' de<PERSON><PERSON><PERSON> 'null' veya 'undefined' o<PERSON><PERSON><PERSON> olasıdır.", "_0_is_possibly_undefined_18048": "'{0}' değerinin 'undefined' o<PERSON><PERSON><PERSON> olasıdır.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' <PERSON><PERSON><PERSON>ne kendi temel ifadesinde doğrudan veya dolaylı olarak başvuruluyor.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' <PERSON><PERSON><PERSON>ne kendi tür ek açıklamasında doğrudan veya dolaylı olarak başvuruluyor.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}' birden çok kez belirtildiğinden bu kullanımın üzerine yazılacak.", "_0_list_cannot_be_empty_1097": "'{0}' listesi bo<PERSON>.", "_0_modifier_already_seen_1030": "'{0}' değiştiricisi zaten görüldü.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "'{0}' değiştiricisi yalnızca bir sı<PERSON><PERSON><PERSON><PERSON><PERSON>, arabirimin veya tür diğer adının tür parametresinde görünebilir", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "'{0}' değiştiricisi yalnızca bir <PERSON>, metodun veya sınıfın tür parametresinde görünebilir", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "'{0}' değiştiricisi bir oluşturucu bildiriminde görüntülenemez.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir modülde veya ad alanı öğesinde görünemez.", "_0_modifier_cannot_appear_on_a_parameter_1090": "'{0}' değiştiricisi bir parametrede görüntülenemez.", "_0_modifier_cannot_appear_on_a_type_member_1070": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir tür üyesinde görünemez.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir tür parametresinde görüne<PERSON>z", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "'{0}' değiştiricisi bir 'using' bild<PERSON>minde görünemez.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "'{0}' değiştiricisi bir 'await using' bild<PERSON>minde görünemez.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir dizin imzasında görünemez.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "'{0}' değiştiricisi bu tip sınıf öğelerinde görünemez.", "_0_modifier_cannot_be_used_here_1042": "'{0}' değiştiricisi burada kullanılamaz.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "'{0}' değiştiricisi bir çevresel bağlamda kullanılamaz.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON>ric<PERSON>, '{1}' değiştiricisi ile birlikte kullanılamaz.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "'{0}' değiştiricisi özel bir tanımlayıcıyla birlikte kullanılamaz.", "_0_modifier_must_precede_1_modifier_1029": "'{0}' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, '{1}' değiştiricisinden önce gelmelidir.", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "'\\{0}' ifadesinden sonra küme ayraçları içine alınmış bir Unicode özellik değeri ifadesi gelmelidir.", "_0_needs_an_explicit_type_annotation_2782": "'{0}' açık bir tür ek açıklamasına ihtiyaç duyuyor.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' ya<PERSON><PERSON><PERSON><PERSON> bir türe başvuruyor, ancak burada bir ad alanı olarak kullanılıyor.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' ya<PERSON><PERSON><PERSON><PERSON> bir türe başvuruyor, ancak burada bir değer olarak kullanılıyor.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' yaln<PERSON><PERSON><PERSON> bir türe başvuruyor, ancak burada bir değer olarak kullanılıyor. '{0}' içinde '{1}' kullanmak mı istediniz?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}' yaln<PERSON><PERSON><PERSON> bir türe başvuruyor ancak burada bir değer olarak kullanılıyor. Hedef kitaplığınızı değiştirmeniz gerekiyor mu? 'lib' derleyici seçeneğini es2015 veya üzeri olarak değiştirmeyi deneyin.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' bir UMD genel ö<PERSON><PERSON> b<PERSON>, ancak geçerli dosya bir modül. Bunun yerine bir içeri aktarma eklemeyi deneyin.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' bir de<PERSON><PERSON> başvuruyor ancak burada tür olarak kullanılıyor. 'typeof {0}' kullanmak mı istediniz?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "'{0}', bir tür olarak çözümlenir ve '{1}' etkinleştirildiğinde yeniden dışa aktarmadan önce bu dosyada yalnızca tür olarak işaretlenmelidir. '{0}' içeri aktarıldığında 'import type' kullanmayı göz önünde bulundurun.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "'{0}', bir tür olarak çözümlenir ve '{1}' etkinleştirildiğinde yeniden dışa aktarmadan önce bu dosyada yalnızca tür olarak işaretlenmelidir. 'export type { varsayılan olarak {0} }' kullanmayı düşünün.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "'{0}', yalnızca tür bildirimi olarak çözümlenir ve 'verbatimModuleSyntax' etkinleştirildiğinde yalnızca tür içeri aktarması kullanılarak içeri aktarılmalıdır.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "'{0}', yalnızca tür bildirimi olarak çözümlenir ve '{1}' etkinleştirildiğinde yeniden dışa aktarmadan önce bu dosyada yalnızca tür olarak işaretlenmelidir. '{0}' içeri aktarıldığında 'import type' kullanmayı göz önünde bulundurun.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "'{0}', yalnızca tür bildirimi olarak çözümlenir ve '{1}' etkinleştirildiğinde yeniden dışa aktarmadan önce bu dosyada yalnızca tür olarak işaretlenmelidir. 'export type { varsayılan olarak {0} }' kullanmayı düşünün.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "'{0}', yalnızca tür bildirimi olarak çözümlenir ve '{1}' etkin olduğunda yalnızca türü yeniden dışarı aktarma kullanılarak yeniden dışarı aktarılmalıdır.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "'{0}', config.j<PERSON> dos<PERSON> 'compilerOptions' nesnesi içinden ayarlanmalıdır", "_0_tag_already_specified_1223": "'{0}' et<PERSON><PERSON> zaten beli<PERSON>.", "_0_was_also_declared_here_6203": "'{0}' <PERSON><PERSON><PERSON> de burada bildirildi.", "_0_was_exported_here_1377": "'{0}' burada dışarı aktarıldı.", "_0_was_imported_here_1376": "'{0}' burada içeri aktarıldı.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "Dönüş türü ek açıklaması olmayan '{0}', örtük olarak '{1}' dön<PERSON>ş türüne sahip.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "Dönüş türü ek açıklaması olmayan '{0}', örtük olarak '{1}' yield türüne sahip.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "'abstract' değiştiricisi yalnızca sınıf, metot veya özellik bildiriminde görünebilir.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "'accessor' değiştiricisi yalnızca özellik bildiriminde görünebilir.", "and_here_6204": "ve buraya.", "arguments_cannot_be_referenced_in_property_initializers_2815": "Özellik başlatıcılarda 'arguments' ö<PERSON><PERSON>ne başvurulamaz.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": <PERSON><PERSON><PERSON> aktarma, dışa aktarma, import.meta, jsx (jsx: react-jsx ile) veya esm biçimi (modül: node16+ ile) içeren dosyaları modül olarak ele alın.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "‘await’ if<PERSON>i bir sınıf statik bloğu içinde kullanılamaz.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "'await' if<PERSON>lerine yalnızca dosya bir modül olduğunda dosyanın en üst düzeyinde izin verilir ancak bu dosyanın içeri veya dışarı aktarma işlemi yok. Bu dosyayı modül yapmak için boş bir 'export {}' eklemeyi deneyin.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "'await' if<PERSON><PERSON>ine yalnızca asenkron işlevler içinde ve modüllerin en üst düzeylerinde izin verilir.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "'await' ifadeleri bir parametre başlatıcısında kullanılamaz.", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' <PERSON><PERSON><PERSON><PERSON> bu ifadenin türü üzerinde etkisi yoktur.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "'await using' ifadelerine yalnızca dosya bir modül olduğunda dosyanın en üst düzeyinde izin verilir ancak bu dosyanın içeri veya dışarı aktarma işlemi yok. Bu dosyayı modül yapmak için boş bir 'export {}' eklemeyi deneyin.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "'await using' ifadelerine yalnızca asenkron işlevler içinde ve modüllerin en üst düzeylerinde izin verilir.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "'await using' ifadeleri bir sınıf statik bloğu içinde kullanılamaz.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "'baseUrl' se<PERSON><PERSON><PERSON><PERSON> '{0}' olarak ayarlandı; g<PERSON><PERSON><PERSON> o<PERSON> '{1}' modül adını çözümlemek için bu değer kullanılıyor.", "c_must_be_followed_by_an_ASCII_letter_1512": "'\\c' ardından bir ASCII harfi gelmelidir.", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' ya<PERSON><PERSON><PERSON><PERSON> dos<PERSON>ın başlangıcında kullanılabilir.", "case_or_default_expected_1130": "'case' veya 'default' if<PERSON><PERSON><PERSON> bekleniyor.", "catch_or_finally_expected_1472": "'catch' veya 'finally' bekleniyor.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "'const' sabit listesi üyesi b<PERSON>ı, sonlu olmayan bir değer olarak he<PERSON>plandı.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "'const' sabit listesi üyesi başlatıcısı, izin verilmeyen 'NaN' değeri olarak he<PERSON>plandı.", "const_enum_member_initializers_must_be_constant_expressions_2474": "const sabit listesi üye başlatıcıları sabit ifadeler olmalıdır.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "'const' sabit list<PERSON><PERSON> yaln<PERSON><PERSON><PERSON> bi<PERSON>, dizin er<PERSON><PERSON><PERSON>, içeri aktarma bildiriminin veya dışarı aktarma atamasının sağ tarafında ya da tür sorgusunda kullanılabilir.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "'constructor', parametre özellik adı olarak kullanılamaz.", "constructor_is_a_reserved_word_18012": "'#constructor' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bir sözcüktür.", "default_Colon_6903": "varsayılan:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "'delete', katı moddaki bir tanımlayıcıda çağrılamaz.", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' varsayılanı yeniden dışarı aktarmaz.", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' yalnızca TypeScript dosyalarında kullanılabilir.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "'export' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, her zaman görün<PERSON><PERSON><PERSON> için çevresel modüllere ve modül genişletmelerine uygulanamaz.", "extends_clause_already_seen_1172": "'extends' yan tümcesi zaten gö<PERSON>.", "extends_clause_must_precede_implements_clause_1173": "'extends' yan tüm<PERSON><PERSON>, 'implements' yan tümcesinden önce gelmelidir.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "Dışarı aktarılan '{0}' sın<PERSON><PERSON>ının 'extends' yan tümcesi, '{1}' özel adına sahip veya bu adı kullanıyor.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "Dışarı aktarılan sınıfın 'extends' yan tümcesi, '{0}' özel adına sahip veya bu adı kullanıyor.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "Dışarı aktarılan '{0}' arabiriminin 'extends' yan tümces<PERSON>, '{1}' özel adına sahip veya bu adı kullanıyor.", "false_unless_composite_is_set_6906": "'Kompozit' ayarlanmamışsa 'false'", "false_unless_strict_is_set_6905": "'<PERSON><PERSON>' ayarlanmamışsa 'false'", "file_6025": "<PERSON><PERSON>", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "'for await' döngülerine yalnızca dosya bir modül olduğunda dosyanın en üst düzeyinde izin verilir ancak bu dosyanın içeri veya dışarı aktarma işlemi yok. Bu dosyayı modül yapmak için boş bir 'export {}' eklemeyi deneyin.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "'for await' <PERSON><PERSON>ng<PERSON><PERSON>ine yalnızca asenkron işlevler içinde ve modüllerin en üst düzeylerinde izin verilir.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "'for await' döngüleri bir sınıf statik bloğu içinde kullanılamaz.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "'get' ve 'set' er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 'this' parametrel<PERSON> bildire<PERSON>z.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "'Files' ayarlanmışsa ‘[]', aksi ta<PERSON> `[\"**/*\"]5D;`", "implements_clause_already_seen_1175": "'implements' yan tümcesi zaten gö<PERSON>.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "'implements' yan tümceleri yalnızca TypeScript dosyalarında kullanılabilir.", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' yalnızca TypeScript dosyalarında kullanılabilir.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "'infer' bi<PERSON><PERSON><PERSON><PERSON> yalnızca bir koşullu türün 'extends' yan tümcesinde izin verilir.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "'\\k' ifadesinden sonra açılı ayraçlar içine alınmış bir yakalama grubu adı gelmelidir.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let' if<PERSON>i, 'let' veya 'const' bild<PERSON><PERSON><PERSON><PERSON> ad olarak kullanılamaz.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === 'AMD' veya 'UMD' veya 'Sistem' veya 'ES6' ve ardından 'Klasik', aksi du<PERSON> 'Node'", "module_system_or_esModuleInterop_6904": "module === \"sistem\" veya esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "Yapı imzası bulunmayan 'new' ifadesi örtük olarak 'any' tür<PERSON>ne sahip.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\"node_modules\", \"bower_components\", \"jspm_packages\"]` ve belirtilmişse ek olarak `outDir`.", "one_of_Colon_6900": "<PERSON>un<PERSON><PERSON> biri:", "one_or_more_Colon_6901": "bir veya daha fazla:", "options_6024": "seçenekler", "or_JSX_element_expected_1145": "'{' veya JSX öğesi bekleniyor.", "or_expected_1144": "'{' veya ';' bekleniyor.", "package_json_does_not_have_a_0_field_6100": "'package.json' geç<PERSON>li bir '{0}' al<PERSON><PERSON>na <PERSON>.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json', '{0}' s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eş<PERSON>şen bir 'typesVersions' girdisine sahip değil.", "package_json_had_a_falsy_0_field_6220": "'package.json' hatalı bir '{0}' alanı içeriyordu.", "package_json_has_0_field_1_that_references_2_6101": "'package.json', '{2}' <PERSON><PERSON><PERSON>ne ba<PERSON>vu<PERSON>a bulunan '{1}' adlı '{0}' alanını içeriyor.", "package_json_has_a_peerDependencies_field_6281": "'package.json', 'peerDependencies' alanı içeriyor.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json', geçerli bir semver aralığı olmayan '{0}' 'typesVersions' girdisine sahip.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json', '{2}' modül adıyla eşle<PERSON>en bir desen arayan '{1}' der<PERSON><PERSON> sür<PERSON>m<PERSON><PERSON> eşleşen '{0}' 'typesVersions' girdisine sahip.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json', s<PERSON><PERSON><PERSON>me özgü yol eşlemeleri olan bir 'typesVersions' alanına sahip.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "'{0}' package.json kapsamı '{1}' tanımlayıcısını açıkça null değerine eşliyor.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "'{0}' package.j<PERSON> ka<PERSON>, '{1}' tan<PERSON><PERSON><PERSON>ıcısının hedefi için geçersiz türe sahip", "package_json_scope_0_has_no_imports_defined_6273": "'{0}' package.json kapsamında içeri aktarma tanımlanmadı.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "'paths' se<PERSON><PERSON><PERSON><PERSON>, '{0}' mod<PERSON><PERSON> ad<PERSON><PERSON> eşleşen bir desen aranıyor.", "q_is_only_available_inside_character_class_1511": "'\\q' yalnızca karakter sınıfının içinde kullanılabilir.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "'\\q' ardından küme ayraçları içine alınmış dize alternatifleri gelmelidir.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "'readonly' değiştiricisi yalnızca özellik bildiriminde ya da dizin imzasında görünebilir.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "'readonly' tür değiştiricisine yalnızca dizide ve demet sabit değeri türlerinde izin verilir.", "require_call_may_be_converted_to_an_import_80005": "'require' çağrısı bir import olarak dönüştürülebilir.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "`resolution-mode` yaln<PERSON>zca tür içeri aktarmaları için ayarlanabilir.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "`resolution-mode`, tür içe aktarma iddiaları için tek geçerli anahtardır.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "`resolution-mode`, tür içe aktarma öznitelikleri için tek geçerli anahtardır.", "resolution_mode_should_be_either_require_or_import_1453": "`resolution-mode`, `require` ya da `import` o<PERSON>l<PERSON>d<PERSON>r.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "'rootDirs' se<PERSON><PERSON><PERSON><PERSON>, '{0}' g<PERSON><PERSON><PERSON> modül adını çözümlemek için bu değer kullanılıyor.", "super_can_only_be_referenced_in_a_derived_class_2335": "'super' öğ<PERSON>ne yalnızca bir türetilmiş sınıfta başvurulabilir.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "'super' de<PERSON><PERSON><PERSON> yalnızca türetilen sınıfların üyelerinde ya da nesne değişmez ifadelerinde başvurulabilir.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "'super' öğesine hesaplanan bir özellik adında başvurulamaz.", "super_cannot_be_referenced_in_constructor_arguments_2336": "'super' ö<PERSON><PERSON>ne oluşturucu bağımsız değişkenlerinde başvurulamaz.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "Nesne değişmez ifadelerinde 'super' değerine yalnızca 'target' seçeneği 'ES2015' veya üzeri olarak ayarlandığında izin verilir.", "super_may_not_use_type_arguments_2754": "'super', tü<PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON>ğişkenlerini kullanamaz.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "Türetilmiş bir sınıfın oluşturucusunda 'super' özelliğine erişmeden önce 'super' çağrılmalıdır.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "Türetilen bir sınıfın oluşturucusundaki 'this' değerine erişilmeden önce 'super' çağrılmalıdır.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' öğesinden sonra bir bağımsız değişken listesi veya üye erişimi gelmelidir.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "'super' özellik erişimine yalnızca bir oluşturucuda, üye işlevinde veya bir türetilmiş sınıfa ait üye erişimcisinde izin verilir.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "'this' öğesine hesaplanan bir özellik adında başvurulamaz.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "'this' öğesine bir modülde veya ad alanı gövdesinde başvurulamaz.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "'this' öğesine statik özellik başlatıcısında başvurulamaz.", "this_cannot_be_referenced_in_current_location_2332": "'this' öğesine geçerli konumda başvurulamaz.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this', tür ek açıklamasına sahip olmadığından örtük olarak 'any' türü içeriyor", "true_for_ES2022_and_above_including_ESNext_6930": "ESNext dahil olmak üzere ES2022 ve üzeri için `true` <PERSON><PERSON><PERSON>ı<PERSON>.", "true_if_composite_false_otherwise_6909": "'Kompozit' ayarlanmışsa 'true', de<PERSON><PERSON><PERSON> 'false'", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "'moduleResolution'; 'node16', 'nodenext' veya 'bundler' <PERSON><PERSON><PERSON><PERSON> `true`, aksi halde `false`.", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: The TypeScript Derleyicisi", "type_Colon_6902": "tür:", "unique_symbol_types_are_not_allowed_here_1335": "Burada 'unique symbol' t<PERSON><PERSON><PERSON> izin verilmez.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "'unique symbol' t<PERSON><PERSON><PERSON> yalnızca bir değişken deyimindeki değişkenlerde izin verilir.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "'unique symbol' t<PERSON><PERSON><PERSON>, bağ<PERSON>a adına sahip bir değişken bildiriminde kullanılamaz.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "'use strict' y<PERSON><PERSON><PERSON><PERSON>, basit olmayan parametre listesiyle birlikte kullanılamıyor.", "use_strict_directive_used_here_1349": "'use strict' yönergesi burada kullanıldı.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "'with' <PERSON><PERSON><PERSON><PERSON> zaman uyumsuz bir işlev bloğunda izin verilmez.", "with_statements_are_not_allowed_in_strict_mode_1101": "'with' <PERSON><PERSON><PERSON><PERSON> katı modda izin verilmez.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Kapsayan oluşturucusunda dönüş türü ek açıklaması olmadığından 'yield' ifadesi örtük olarak 'any' türü ile sonuçlanır.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "'yield' ifadeleri bir parametre başlatıcısında kullanılamaz."}